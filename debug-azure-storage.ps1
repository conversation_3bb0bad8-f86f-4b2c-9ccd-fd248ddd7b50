# Azure Storage Debug Script
# This script helps debug Azure Storage configuration issues

Write-Host "=== Azure Storage Configuration Debug ===" -ForegroundColor Green

# Set your credentials here
$env:AZCOPY_AUTO_LOGIN_TYPE = "SPN"
$env:A<PERSON>COPY_SPA_APPLICATION_ID = "YOUR_APP_ID_HERE"
$env:AZCOPY_SPA_CLIENT_SECRET = "YOUR_CLIENT_SECRET_HERE"
$env:AZCOPY_TENANT_ID = "YOUR_TENANT_ID_HERE"

$STORAGE_ACCOUNT = "inconnecttestreports"
$STORAGE_URL = "https://$STORAGE_ACCOUNT.z13.web.core.windows.net"

Write-Host "Storage Account: $STORAGE_ACCOUNT" -ForegroundColor Yellow
Write-Host "Storage URL: $STORAGE_URL" -ForegroundColor Yellow

# Test 1: Basic authentication
Write-Host "`n=== Test 1: Authentication ===" -ForegroundColor Cyan
try {
    Write-Host "Testing authentication..."
    azcopy list "$STORAGE_URL/`$web" --output-type=text
    Write-Host "✅ Authentication successful!" -ForegroundColor Green
} catch {
    Write-Host "❌ Authentication failed: $_" -ForegroundColor Red
    Write-Host "Check your SPN credentials and permissions." -ForegroundColor Yellow
}

# Test 2: Container access
Write-Host "`n=== Test 2: Container Access ===" -ForegroundColor Cyan
try {
    Write-Host "Listing `$web container contents..."
    azcopy list "$STORAGE_URL/`$web" --output-type=json | ConvertFrom-Json | Format-Table
    Write-Host "✅ Container access successful!" -ForegroundColor Green
} catch {
    Write-Host "❌ Container access failed: $_" -ForegroundColor Red
    Write-Host "The `$web container might not exist or you don't have permissions." -ForegroundColor Yellow
}

# Test 3: Different path formats
Write-Host "`n=== Test 3: Path Format Testing ===" -ForegroundColor Cyan

$testContent = "Test content $(Get-Date)"
$testContent | Out-File -FilePath "debug-test.txt" -Encoding UTF8

$testPaths = @(
    "$STORAGE_URL/`$web/debug-test-1.txt",
    "$STORAGE_URL/`$web/test-folder/debug-test-2.txt",
    "$STORAGE_URL/`$web/allure/debug-test-3.txt"
)

foreach ($path in $testPaths) {
    Write-Host "Testing path: $path" -ForegroundColor Yellow
    try {
        azcopy cp "./debug-test.txt" "$path" --from-to=LocalBlob
        Write-Host "✅ Success: $path" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed: $path - $_" -ForegroundColor Red
    }
}

Remove-Item "debug-test.txt" -ErrorAction SilentlyContinue

# Test 4: Check storage account type
Write-Host "`n=== Test 4: Storage Account Information ===" -ForegroundColor Cyan
Write-Host "Storage Account: $STORAGE_ACCOUNT"
Write-Host "Expected URL: $STORAGE_URL"
Write-Host "Container: `$web (static website container)"
Write-Host ""
Write-Host "Common issues to check:" -ForegroundColor Yellow
Write-Host "1. Is static website hosting enabled on the storage account?"
Write-Host "2. Does the `$web container exist?"
Write-Host "3. Does your SPN have 'Storage Blob Data Contributor' role?"
Write-Host "4. Is the storage account accessible from the internet?"
Write-Host "5. Are there any firewall rules blocking access?"

Write-Host "`n=== Debug Complete ===" -ForegroundColor Green
Write-Host "If all tests pass but GitHub Actions still fails, the issue might be:" -ForegroundColor Yellow
Write-Host "- GitHub Actions IP range not allowed in storage account firewall"
Write-Host "- Different authentication context in GitHub Actions"
Write-Host "- File path or naming issues specific to the CI environment"
