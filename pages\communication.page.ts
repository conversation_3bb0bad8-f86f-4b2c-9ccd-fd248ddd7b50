import { Page } from '@playwright/test';
import { Communication_objects } from "../test-objects/communication_objects";

export class CommunicationPage {
    private page: Page;
    private communicationObjects: Communication_objects;

    constructor(page: Page) {
        this.page = page;
        this.communicationObjects = new Communication_objects(page);
    }

    async navigateToMessageGroups() {
        await this.communicationObjects.communicationsLocators();
    }

    async createNewMessageGroup() {
        await this.page.getByRole('button', { name: 'Add New User Group' }).click();
        const groupNameField = this.page.getByLabel('Give your group a name:');
        if (await groupNameField.isVisible()) {
            console.log("Group name field is visible");
        } else {
            console.log("Group name field is not visible");
        }
    }

    async backToMessageGroups() {
        await this.page.waitForTimeout(2000);
        const tdBackNav = this.page.getByRole("cell", { name: " Message Group" });
        await tdBackNav.click();
        await this.page.waitForTimeout(3000);
    }

    async searchRandomMessageGroup() {
        const randomGroup = await this.communicationObjects.randomMessageGroup();
        await this.page.getByRole('link', { name: 'Apply' }).click();
        await this.page.waitForTimeout(1000);
        return randomGroup;
    }

    async isMessageGroupDataGridVisible() {
        const messageDataGrid = await this.page.$('#enrol-table tbody tr');
        return messageDataGrid ? messageDataGrid.isVisible() : false;
    }

    async navigateToBulkCommunications() {
        await this.page.getByText('Communication', { exact: true }).click();
        await this.page.getByRole('link', { name: 'Bulk Communications' }).click();
        await this.page.getByRole('heading', { name: 'Bulk Communications Setup' }).getByRole('button').click();
        return this.communicationObjects.messageName();
    }

    async navigateToTriggeredCommunications() {
        await this.page.getByText('Communication', { exact: true }).click();
        await this.page.getByRole('link', { name: 'Triggered Communications' }).click();
        await this.page.waitForTimeout(6000);
        await this.page.getByRole('heading', { name: 'Triggered Communications' }).getByRole('button').click();
        return this.communicationObjects.messageName();
    }

    async navigateToScheduledCommunications() {
        await this.page.getByText('Communication', { exact: true }).click();
        await this.page.getByRole('link', { name: 'Scheduled Communications' }).click();
        await this.page.getByRole('heading', { name: 'Scheduled Communications' }).getByRole('button').click();
        return this.communicationObjects.messageName();
    }

    async navigateToSurveyCommunications() {
        await this.page.getByText('Communication', { exact: true }).click();
        await this.page.getByRole('link', { name: 'Survey Communications' }).click();
        await this.page.waitForTimeout(3000);
        await this.page.getByRole('button', { name: 'Setup Communication' }).nth(1).click();
        await this.page.waitForTimeout(3000);
        return this.communicationObjects.messageName();
    }
}
