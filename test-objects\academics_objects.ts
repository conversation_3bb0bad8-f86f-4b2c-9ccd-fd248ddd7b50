import {Page} from "@playwright/test";

export class Academics_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    async academicsLocator(): Promise<void> {
        await this.page.click('div[data-toggle="collapse"][data-target="#Academics_collapse"]'); // Click on academics navbar
        await this.page.click('a:has-text("Courses")'); // Click on Courses link
        await this.page.waitForTimeout(3000);
    }

    /**
     * Applies random filters to the Courses section.
     * Selects a random qualification type and academic year, then applies the filter.
     * @returns {Promise<void>}
     */
    async filterCourses(): Promise<void> {
        await this.page.waitForTimeout(3000);
        const randomValue = Math.floor(Math.random() * 3) + 2;
        const randomValueStr = randomValue.toString();
        
        // Verify options exist before selecting
        const options = await this.page.$$('select[name="c36"] option');
        if (options.length === 0) {
            throw new Error('No options found in qualification type select element');
        }
        
        await this.page.selectOption('select[name="c36"]', randomValueStr); // Select random qualification type
        console.log(`Selected qualification type: ${randomValueStr}`)

        let academicValue: number;
        if (randomValue === 4) {
            academicValue = 1; // If qualification type is "Higher Cert", select "1st Year"
        } else {
            academicValue = Math.floor(Math.random() * 3) + 1; // Otherwise, select a random academic year
        }
        const academicValueStr = academicValue.toString();
        
        // Verify academic year options exist before selecting
        const academicOptions = await this.page.$$('select[name="c33"] option');
        if (academicOptions.length === 0) {
            throw new Error('No options found in academic year select element');
        }
        
        await this.page.selectOption('select[name="c33"]', academicValueStr); // Select a random academic year
        console.log(`Selected academic year: ${academicValueStr}`)
        await this.page.click('div#FiltersCollapse div.panel-body div:nth-child(5) div button[name="c38"]'); // Click apply filter
        await this.page.waitForTimeout(5000); // Wait for data to load
    }

    async randomCourse(): Promise<void> {
        const courses = await this.page.$$(
            'div#courses div.row div div:nth-child(2) div.table-responsive table tbody tr td:first-child'
        );
        const randomCourseIndex = Math.floor(Math.random() * (courses.length - 1));
        console.log(`Random course index: ${randomCourseIndex}`);
        await courses[randomCourseIndex].click();
        await this.page.waitForTimeout(3000);
    }

    /**
     * Selects a random course from the Courses section and navigates to the Subject tab.
     * @returns {Promise<void>}
     */
    async randomCourseAndSubjectTab(): Promise<void> {
        const courses = await this.page.$$(
            'div#courses div.row div div:nth-child(2) div.table-responsive table tbody tr td:first-child'
        );
        const randomCourseIndex = Math.floor(Math.random() * (courses.length - 1));
        console.log(`Random course index: ${randomCourseIndex}`);
        await courses[randomCourseIndex].click();
        await this.page.waitForTimeout(6000);
        const linkTab = this.page.getByRole("tab", {name: "Subjects"});
        await linkTab.click();
        await this.page.waitForTimeout(8000);
    }

    /**
     * Selects a random subject from the Subject tab.
     * @returns {Promise<void>}
     */
    async selectRandomSubject(): Promise<void> {
        const subjects = await this.page.$$(
            '#c96_dataGrid tbody tr'
        );
        if (subjects.length > 0) {
            const randomSubjectIndex = Math.floor(Math.random() * subjects.length);
            await subjects[randomSubjectIndex].click(); // Click on a random subject
            await this.page.waitForTimeout(3000); // Wait for subject details to load
        } else {
            console.error("No subjects found in the table 'c96_dataGrid'");
        }
    }

    /**
     * Applies random filters to the Courses section for offsite courses.
     * Selects a random qualification type and academic year, then applies the filter.
     * @returns {Promise<void>}
     */
    async filterCoursesOffsite(): Promise<void> {
        await this.page.waitForTimeout(3000);
        const randomValue = Math.floor(Math.random() * 2) + 2;
        const randomValueStr = randomValue.toString();
        
        // Verify options exist before selecting
        const options = await this.page.$$('select[name="c36"] option');
        if (options.length === 0) {
            throw new Error('No options found in qualification type select element');
        }
        
        await this.page.selectOption('select[name="c36"]', randomValueStr); // Select random qualification type
        console.log(`Selected qualification type: ${randomValueStr}`)

        let academicValue: number;
        if (randomValue === 4) {
            academicValue = 1; // If qualification type is "Higher Cert", select "1st Year"
        } else {
            academicValue = Math.floor(Math.random()) + 3; // Otherwise, select a random academic year
        }
        const academicValueStr = academicValue.toString();
        
        // Verify academic year options exist before selecting
        const academicOptions = await this.page.$$('select[name="c33"] option');
        if (academicOptions.length === 0) {
            throw new Error('No options found in academic year select element');
        }
        
        await this.page.selectOption('select[name="c33"]', academicValueStr); // Select a random academic year
        console.log(`Selected academic year: ${academicValueStr}`)
        await this.page.click('div#FiltersCollapse div.panel-body div:nth-child(5) div button[name="c38"]'); // Click apply filter
        await this.page.waitForTimeout(5000); // Wait for data to load
    }

    /**
     * Applies random filters to the Courses section for Rise360 courses.
     * Selects a random qualification type and academic year, then applies the filter.
     * @returns {Promise<void>}
     */
    async filterCoursesRise360(): Promise<void> {
        await this.page.waitForTimeout(3000);
        const randomValue = Math.floor(Math.random()) + 2;
        const randomValueStr = randomValue.toString();
        
        // Verify options exist before selecting
        const options = await this.page.$$('select[name="c36"] option');
        if (options.length === 0) {
            throw new Error('No options found in qualification type select element');
        }
        
        await this.page.selectOption('select[name="c36"]', randomValueStr); // Select random qualification type
        console.log(`Selected qualification type: ${randomValueStr}`)

        let academicValue: number;
        if (randomValue === 4) {
            academicValue = 1; // If qualification type is "Higher Cert", select "1st Year"
        } else {
            academicValue = Math.floor(Math.random()) + 1; // Otherwise, select a random academic year
        }
        const academicValueStr = academicValue.toString();
        
        // Verify academic year options exist before selecting
        const academicOptions = await this.page.$$('select[name="c34"] option');
        if (academicOptions.length === 0) {
            throw new Error('No options found in academic year select element');
        }
        
        await this.page.selectOption('select[name="c34"]', academicValueStr); // Select a random academic year
        console.log(`Selected academic year: ${academicValueStr}`)
        await this.page.click('div#FiltersCollapse div.panel-body div:nth-child(5) div button[name="c38"]'); // Click apply filter
        await this.page.waitForTimeout(5000); // Wait for data to load
    }
}
