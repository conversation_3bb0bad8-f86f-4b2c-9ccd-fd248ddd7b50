# Test Azure Storage Upload Script
# This script tests the same upload process locally

Write-Host "=== Azure Storage Upload Test ===" -ForegroundColor Green

# IMPORTANT: Replace these with your actual values from GitHub Secrets
Write-Host "⚠️  IMPORTANT: Update the credentials below with your actual values!" -ForegroundColor Red
Write-Host "You can find these in your GitHub repository Settings > Secrets and variables > Actions" -ForegroundColor Yellow
Write-Host ""

# Set environment variables (REPLACE WITH YOUR ACTUAL VALUES)
$env:AZCOPY_AUTO_LOGIN_TYPE = "SPN"
$env:AZCOPY_SPA_APPLICATION_ID = "823dc0c0-a40b-4e6c-85c0-2848c21ce7c8"  # Replace with actual value
$env:AZCOPY_SPA_CLIENT_SECRET = "****************************************"  # Replace with actual value
$env:AZCOPY_TENANT_ID = "de9bcee7-1e9c-4878-9fb7-39b9116e9964"  # Replace with actual value (format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx)

# Validate credentials are set
if ($env:AZCOPY_SPA_APPLICATION_ID -eq "YOUR_APP_ID_HERE" -or
    $env:AZCOPY_SPA_CLIENT_SECRET -eq "YOUR_CLIENT_SECRET_HERE" -or
    $env:AZCOPY_TENANT_ID -eq "YOUR_TENANT_ID_HERE") {
    Write-Host "❌ ERROR: Please update the credentials in this script with your actual values!" -ForegroundColor Red
    Write-Host "Edit this file and replace YOUR_APP_ID_HERE, YOUR_CLIENT_SECRET_HERE, and YOUR_TENANT_ID_HERE" -ForegroundColor Yellow
    exit 1
}

# Test variables
$REPORT_DIR = "test-results-$(Get-Date -Format 'yyyyMMddHHmmss')"
$STORAGE_URL = "https://inconnecttestreports.blob.core.windows.net/`$web"

Write-Host "Report Directory: $REPORT_DIR" -ForegroundColor Yellow
Write-Host "Storage URL: $STORAGE_URL" -ForegroundColor Yellow

# Step 1: Test connection
Write-Host "`n=== Step 1: Testing Connection ===" -ForegroundColor Cyan
Write-Host "Testing list command..."
Write-Host "Command: azcopy list `"$STORAGE_URL`" --output-type=json" -ForegroundColor Gray

$result = & azcopy list "$STORAGE_URL" --output-type=json 2>&1
$exitCode = $LASTEXITCODE

Write-Host "Exit code: $exitCode" -ForegroundColor Gray
Write-Host "Output: $result" -ForegroundColor Gray

if ($exitCode -eq 0) {
    Write-Host "✅ Connection test passed!" -ForegroundColor Green
} else {
    Write-Host "❌ Connection test failed!" -ForegroundColor Red
    Write-Host "This usually means:" -ForegroundColor Yellow
    Write-Host "- Invalid credentials" -ForegroundColor Yellow
    Write-Host "- Storage account doesn't exist" -ForegroundColor Yellow
    Write-Host "- `$web container doesn't exist (static website not enabled)" -ForegroundColor Yellow
    exit 1
}

# Step 2: Test simple file upload
Write-Host "`n=== Step 2: Testing Simple File Upload ===" -ForegroundColor Cyan
$testContent = "Test file created at $(Get-Date)"
$testContent | Out-File -FilePath "test-file.txt" -Encoding UTF8

$testFileName = "test-file-$(Get-Date -Format 'yyyyMMddHHmmss').txt"
Write-Host "Uploading test file..."
Write-Host "Command: azcopy cp `"./test-file.txt`" `"$STORAGE_URL/$testFileName`" --from-to=LocalBlob" -ForegroundColor Gray

$result = & azcopy cp "./test-file.txt" "$STORAGE_URL/$testFileName" --from-to=LocalBlob 2>&1
$exitCode = $LASTEXITCODE

Write-Host "Exit code: $exitCode" -ForegroundColor Gray
Write-Host "Output: $result" -ForegroundColor Gray

if ($exitCode -eq 0) {
    Write-Host "✅ Simple file upload passed!" -ForegroundColor Green
    Write-Host "File should be available at: $STORAGE_URL/$testFileName" -ForegroundColor Yellow
} else {
    Write-Host "❌ Simple file upload failed!" -ForegroundColor Red
}

Remove-Item "test-file.txt" -ErrorAction SilentlyContinue

# Step 3: Test directory upload (if you have allure-report directory)
Write-Host "`n=== Step 3: Testing Directory Upload ===" -ForegroundColor Cyan

if (Test-Path "allure-report") {
    Write-Host "Found allure-report directory. Testing upload..."
    
    # Method 1: Upload entire directory
    Write-Host "Method 1: Upload entire directory"
    try {
        azcopy cp "./allure-report" "$STORAGE_URL/allure/$REPORT_DIR" --recursive --from-to=LocalBlob
        Write-Host "✅ Method 1 succeeded!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Method 1 failed: $_" -ForegroundColor Red
        
        # Method 2: Upload contents
        Write-Host "Method 2: Upload directory contents"
        try {
            azcopy cp "./allure-report/*" "$STORAGE_URL/allure/$REPORT_DIR/" --recursive --from-to=LocalBlob
            Write-Host "✅ Method 2 succeeded!" -ForegroundColor Green
        } catch {
            Write-Host "❌ Method 2 also failed: $_" -ForegroundColor Red
        }
    }
    
    Write-Host "`nTest report should be available at:"
    Write-Host "$STORAGE_URL/allure/$REPORT_DIR/index.html" -ForegroundColor Yellow
} else {
    Write-Host "No allure-report directory found. Skipping directory upload test." -ForegroundColor Yellow
    Write-Host "To test with actual reports, run 'npm test' first to generate reports." -ForegroundColor Yellow
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
