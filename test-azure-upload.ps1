# Test Azure Storage Upload Script
# This script tests the same upload process locally

Write-Host "=== Azure Storage Upload Test ===" -ForegroundColor Green

# Set environment variables (you'll need to set these)
$env:AZCOPY_AUTO_LOGIN_TYPE = "SPN"
$env:A<PERSON>COPY_SPA_APPLICATION_ID = "YOUR_APP_ID_HERE"
$env:AZCOPY_SPA_CLIENT_SECRET = "YOUR_CLIENT_SECRET_HERE"
$env:AZCOPY_TENANT_ID = "YOUR_TENANT_ID_HERE"

# Test variables
$REPORT_DIR = "test-run-$(Get-Date -Format 'yyyyMMddHHmmss')"
$STORAGE_URL = "https://inconnecttestreports.z13.web.core.windows.net"

Write-Host "Report Directory: $REPORT_DIR" -ForegroundColor Yellow
Write-Host "Storage URL: $STORAGE_URL" -ForegroundColor Yellow

# Step 1: Test connection
Write-Host "`n=== Step 1: Testing Connection ===" -ForegroundColor Cyan
try {
    Write-Host "Testing list command..."
    azcopy list "$STORAGE_URL/`$web" --output-type=json
    Write-Host "✅ Connection test passed!" -ForegroundColor Green
} catch {
    Write-Host "❌ Connection test failed: $_" -ForegroundColor Red
    exit 1
}

# Step 2: Test simple file upload
Write-Host "`n=== Step 2: Testing Simple File Upload ===" -ForegroundColor Cyan
$testContent = "Test file created at $(Get-Date)"
$testContent | Out-File -FilePath "test-file.txt" -Encoding UTF8

try {
    Write-Host "Uploading test file..."
    azcopy cp "./test-file.txt" "$STORAGE_URL/`$web/test-file-$(Get-Date -Format 'yyyyMMddHHmmss').txt" --from-to=LocalBlob
    Write-Host "✅ Simple file upload passed!" -ForegroundColor Green
} catch {
    Write-Host "❌ Simple file upload failed: $_" -ForegroundColor Red
}

Remove-Item "test-file.txt" -ErrorAction SilentlyContinue

# Step 3: Test directory upload (if you have allure-report directory)
Write-Host "`n=== Step 3: Testing Directory Upload ===" -ForegroundColor Cyan

if (Test-Path "allure-report") {
    Write-Host "Found allure-report directory. Testing upload..."
    
    # Method 1: Upload entire directory
    Write-Host "Method 1: Upload entire directory"
    try {
        azcopy cp "./allure-report" "$STORAGE_URL/`$web/allure/$REPORT_DIR" --recursive --from-to=LocalBlob
        Write-Host "✅ Method 1 succeeded!" -ForegroundColor Green
    } catch {
        Write-Host "❌ Method 1 failed: $_" -ForegroundColor Red
        
        # Method 2: Upload contents
        Write-Host "Method 2: Upload directory contents"
        try {
            azcopy cp "./allure-report/*" "$STORAGE_URL/`$web/allure/$REPORT_DIR/" --recursive --from-to=LocalBlob
            Write-Host "✅ Method 2 succeeded!" -ForegroundColor Green
        } catch {
            Write-Host "❌ Method 2 also failed: $_" -ForegroundColor Red
        }
    }
    
    Write-Host "`nTest report should be available at:"
    Write-Host "$STORAGE_URL/allure/$REPORT_DIR/index.html" -ForegroundColor Yellow
} else {
    Write-Host "No allure-report directory found. Skipping directory upload test." -ForegroundColor Yellow
    Write-Host "To test with actual reports, run 'npm test' first to generate reports." -ForegroundColor Yellow
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
