import { Page } from '@playwright/test';
import { GraduationSelectors, GraduationStatus } from '../test-objects/graduation_objects';

export class GraduationPage {
    constructor(private page: Page) {}

    // Navigation
    async navigateToGraduation() {
        await this.page.click('a:has-text("Graduation")');
        // Wait for the header to ensure we're on the right page
        await this.page.waitForSelector('h3:has-text("Graduation Overview")');
    }

    // Tab Navigation
    async navigateToTab(status: GraduationStatus) {
        const tabMapping = {
            [GraduationStatus.IN_PROGRESS]: GraduationSelectors.tabs.inProgress,
            [GraduationStatus.VERIFIED]: GraduationSelectors.tabs.verified,
            [GraduationStatus.READY_FOR_GRADUATION]: GraduationSelectors.tabs.readyForGraduation,
            [GraduationStatus.ARCHIVED]: GraduationSelectors.tabs.archived,
            [GraduationStatus.FAILED]: GraduationSelectors.tabs.failed
        };

        await this.page.click(tabMapping[status]);
    }

    // Verification methods
    async isGraduationOverviewPage(): Promise<boolean> {
        const header = await this.page.locator('h3:has-text("Graduation Overview")');
        return await header.isVisible();
    }

    async isGraduationDetailsModalVisible(): Promise<boolean> {
        const modalHeader = await this.page.locator(GraduationSelectors.modal.header);
        return await modalHeader.isVisible();
    }

    // Actions
    private readonly PAGE_SIZE = 10; // Maximum number of students per page

    async selectRandomStudent() {
        // Find the active table in the current tab
        const table = this.page.locator(GraduationSelectors.dataGrid.activeTabTable);
        const rows = table.locator(GraduationSelectors.dataGrid.studentRow);
        
        // Get count but limit to first page
        const count = await this.getStudentCount();
        if (count === 0) {
            throw new Error('No students found in the graduation list');
        }

        // Ensure we only select from visible rows on first page
        const randomIndex = Math.floor(Math.random() * Math.min(count, this.PAGE_SIZE));
        await rows.nth(randomIndex).click();
        await this.waitForModalLoad();
    }

    async getStudentCount(): Promise<number> {
        const table = this.page.locator(GraduationSelectors.dataGrid.activeTabTable);
        const rows = table.locator(GraduationSelectors.dataGrid.studentRow);
        const totalCount = await rows.count();
        // Return actual count up to PAGE_SIZE
        return Math.min(totalCount, this.PAGE_SIZE);
    }

    async getGraduationDate(): Promise<string> {
        await this.waitForField(GraduationSelectors.modal.graduationDate);
        const dateField = this.page.locator(GraduationSelectors.modal.graduationDate);
        return await dateField.inputValue();
    }

    async getStudentCampus(): Promise<string> {
        await this.waitForField(GraduationSelectors.modal.campus);
        const campusField = this.page.locator(GraduationSelectors.modal.campus);
        return (await campusField.inputValue()).trim();
    }

    // Verification methods for read-only status
    async isGraduationDateReadOnly(): Promise<boolean> {
        const graduationDateField = this.page.locator(GraduationSelectors.modal.graduationDate);
        const isDisabled = await graduationDateField.evaluate(
            (element) => element.hasAttribute('disabled') || element.hasAttribute('readonly')
        );
        return isDisabled;
    }

    async saveGraduationDetails() {
        await this.page.click(GraduationSelectors.modal.saveButton);
    }

    async archiveGraduation() {
        await this.page.click(GraduationSelectors.modal.archiveButton);
    }

    async deleteGraduation() {
        await this.page.click(GraduationSelectors.modal.deleteButton);
    }

    async closeGraduationModal() {
        await this.page.click(GraduationSelectors.modal.cancelButton);
    }

    // Getters for page elements
    getGraduationHeader() {
        return this.page.locator('h3:has-text("Graduation Overview")');
    }

    getGraduationDetailsModal() {
        return this.page.locator(GraduationSelectors.modal.header);
    }

    // Helper methods
    private async waitForModalLoad() {
        // Wait for modal header
        await this.page.waitForSelector(GraduationSelectors.modal.header);

        // Additional stability wait
        await this.page.waitForTimeout(500);
    }

    async getModalStudentName(): Promise<string> {
        const studentNameField = this.page.locator(GraduationSelectors.modal.studentName);
        return await studentNameField.inputValue();
    }

    private async waitForField(selector: string): Promise<void> {
        const field = this.page.locator(selector);
        await field.waitFor({ state: 'visible', timeout: 10000 });
        // Small wait to ensure field is stable
        await this.page.waitForTimeout(100);
    }
}
