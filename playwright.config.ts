import { defineConfig, devices } from '@playwright/test';
import * as os from "os";
import dotenv from 'dotenv';

const envName = process.env.ENV || "prod";

if (!envName) {
  throw new Error("Environment variable ENV is not set.");
}

dotenv.config({
  path: `./env/.env.${envName}`,
})

export default defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 2,
  /* Opt out of parallel tests on CI. */
  workers: 2,
  reporter: [
    ["line"],
    [
      "allure-playwright",
      {
        detail: true,
        outputFolder: "allure-results",
        suiteTitle: true,
        environmentInfo: {
          framework: "Playwright",
          environment: envName,
          os_platform: os.platform(),
          os_release: os.release(),
          os_version: os.version(),
          node_version: process.version,
        }
      },
    ],
    ["html", { outputFolder: "playwright-report" }],
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  timeout: 250000,
  globalSetup: require.resolve('./tests/auth.setup.ts'),
  globalTeardown: require.resolve('./tests/global-teardown.ts'),
  outputDir: 'test-results',
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    colorScheme: 'dark',
    /* Accept downloads by default */
    acceptDownloads: true,
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on',
    launchOptions: {
      logger: {
        isEnabled: () => true,
        log: (name, severity, message) => console.log(name, severity, message)
      },
    }
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'bot-user-tests',
      testMatch: /.*\.spec\.ts/,
      use: { 
        ...devices['Desktop Chrome'],
        headless: true,
        storageState: 'playwright/.auth/bot-user.json',
      },
    },
    {
      name: 'helper-bot-tests',
      testMatch: /.*\.spec\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        headless: true,
        storageState: 'playwright/.auth/helper-bot-user.json',
      },
    },
    {
      name: 'approved-tests',
      testMatch: /approved_tests\/.*\.spec\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        headless: true,
        storageState: 'playwright/.auth/bot-user.json',
      },
      testIgnore: /.*\.setup\.ts/,
    },
    {
      name: 'not-approved-tests',
      testMatch: /not_approved\/.*\.spec\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        headless: true,
        storageState: 'playwright/.auth/bot-user.json',
      },
      testIgnore: /.*\.setup\.ts/,
    },
  ],
});
