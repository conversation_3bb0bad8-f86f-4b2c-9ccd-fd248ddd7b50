import {Page} from "@playwright/test";

export class Analytics_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Applies random filters to the Courses section for Rise360 courses.
     * Selects a random qualification type and academic year, then applies the filter.
     * @returns {Promise<void>}
     */
    async reportManagerLocator(): Promise<void> {
        await this.page.click('div#SystemConfiguration_dropdown'); // Navigate to system configuration
        await this.page.click('text=Report Manager'); // Navigate to report manager
        await this.page.waitForTimeout(5000); // Wait for the page to load
    }

    /**
     * Navigates to the Data Analytics section in the left sidebar.
     * @returns {Promise<void>}
     */
    async dataAnalyticsLocator(): Promise<void> {
        await this.page.locator('#LeftSideBar div').filter({hasText: 'Data Analytics'}).nth(1).click(); // Navigate to data analytics
        await this.page.waitForTimeout(5000); // Wait for 5 seconds
    }

    /**
     * Selects a random report from the data grid and enters its name in the search bar.
     * @returns {Promise<string>} The name of the randomly selected report.
     */
    async randomReportSelection(): Promise<string> {const dataGridRows = await this.page.$$('//div[@id="ReportsDataGridObj-grid"]//table/tbody/tr');
        const numberOfRows = dataGridRows.length; // Get the number of rows
        const reports: string[] = [];

        for (let i = 1; i <= numberOfRows; i++) { // Loop through each row
            const elementXPath = `//div[@id='ReportsDataGridObj-grid']//table/tbody/tr[${i}]/td[1]`; // XPath for the row
            const option = await this.page.waitForSelector(elementXPath); // Wait for the option and get its text
            const optionText = await option.textContent();
            reports.push(optionText); // Add the option text to the reports list
        }
        const randomSearch = reports[Math.floor(Math.random() * reports.length)]; // Choose a random report from the list
        const searchBar = this.page.locator('//*[@id="ReportsDataGridObj-jdgrid-search"]'); // Enter the report name in the search bar
        await searchBar.fill(randomSearch);
        console.log(`Random search: ${randomSearch}`);
        return randomSearch;
    }

    /**
     * Finds a row in the data grid that matches the given random search string.
     * @param {string} randomSearch - The name of the randomly selected report to search for.
     * @returns {Promise<void>}
     */
    async findRowWithRandomSearch(randomSearch: string): Promise<void> {
        const reportDataGrid = await this.page.$$('//div[@id="ReportsDataGridObj-grid"]//table/tbody/tr');
        for (let i = 0; i < reportDataGrid.length; i++) {
            const gridText = await reportDataGrid[i].textContent();
            if (gridText.includes(randomSearch)) {
                console.log(`Found the report: ${randomSearch}`);
                return;
            }
        }
        console.log(`Could not find the report: ${randomSearch}`);
    }
}