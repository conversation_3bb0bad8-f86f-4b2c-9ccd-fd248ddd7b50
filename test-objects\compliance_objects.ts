import { Page } from "@playwright/test";

export class ComplianceObjects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Navigate to the Compliance section.
     * @returns {Promise<void>}
     */
    async navigateToCompliance() {
        await this.page.getByRole('link', { name: 'Compliance' }).click();
    }

    /**
     * Selects a category by its name.
     * @param {string} categoryName - The name of the category to select.
     * @returns {Promise<void>}
     */
    async selectCategory(categoryName: string) {
        await this.page.getByRole('cell', { name: categoryName }).click();
    }

    /**
     * Selects a random campus from the available radio button options.
     * @returns {Promise<void>}
     */
    async selectCampus() {
        const campusOptions = await this.page.$$eval('.radio input[type="radio"]', (inputs) => inputs.map(input => input.id));
        const randomIndex = Math.floor(Math.random() * campusOptions.length);
        const campusId = campusOptions[randomIndex];
        await this.page.click(`label[for="${campusId}"]`);
    }

    /**
     * Clicks the Generate Report button.
     * @returns {Promise<void>}
     */
    async generateReport() {
        await this.page.getByRole('button', { name: 'Generate Report' }).click();
    }
}