import { Page } from '@playwright/test';
import { Classroom_management_objects, Attendance_Overview_objects, Student_Success_Overview_objects, Timetable_objects, LateResub_objects, DateFilter } from "../test-objects/classroom_management";
import { StudentSuccessDetailData, readStudentSuccessDetailCSV } from "../test-objects/csv-utils";

export class ClassroomManagementPage {
    public page: Page;
    private classroomManagementObjects: Classroom_management_objects;
    private attendanceOverviewObjects: Attendance_Overview_objects;
    private studentSuccessOverviewObjects: Student_Success_Overview_objects;
    private timetableObjects: Timetable_objects;
    private lateResubObjects: LateResub_objects;
    private studentSuccessDetailData: StudentSuccessDetailData[] | null = null;

    constructor(page: Page) {
        this.page = page;
        this.classroomManagementObjects = new Classroom_management_objects(page);
        this.attendanceOverviewObjects = new Attendance_Overview_objects(page);
        this.studentSuccessOverviewObjects = new Student_Success_Overview_objects(page);
        this.timetableObjects = new Timetable_objects(page);
        this.lateResubObjects = new LateResub_objects(page);
    }

    /**
     * Loads the CSV data for student success detail if not already loaded
     */
    private async loadStudentSuccessDetailData(): Promise<void> {
        if (!this.studentSuccessDetailData) {
            this.studentSuccessDetailData = await readStudentSuccessDetailCSV('data/student-success-detail.csv');
        }
    }

    async navigateToAttendance() {
        await this.classroomManagementObjects.attendanceLocator();
    }

    async checkAttendanceWithPresentFilter() {
        await this.attendanceOverviewObjects.attendancePresentFilter();
    }

    async checkAttendanceWithDateFilter() {
        await this.attendanceOverviewObjects.attendanceDateFilter();
    }

    async toggleToStudentCompliance() {
        const studentToggle = this.page.locator('//div[@id=\'AttendanceOverview\']/div/div[1]/div/span/div/label/span');
        await studentToggle.click();
        await this.page.waitForTimeout(4000);
    }

    async navigateToAttendanceDetail() {
        const attendanceDetailLocator = 'text=Attendance Detail';
        const timeout = 30000; // 30 seconds

        try {
            // Wait for the element to be visible
            await this.page.waitForSelector(attendanceDetailLocator, { state: 'visible', timeout });
            const element = this.page.locator(attendanceDetailLocator);

            // Check if the element is enabled
            const isEnabled = await element.isEnabled();
            const isVisible = await element.isVisible();

            console.log(`Attendance Detail element: Visible - ${isVisible}, Enabled - ${isEnabled}`);

            if (isVisible && isEnabled) {
                await element.click();
                console.log('Clicked on "Attendance Detail"');
            } else {
                const errorMessage = `Attendance Detail element not interactive: Visible - ${isVisible}, Enabled - ${isEnabled}`;
                console.error(errorMessage);
                throw new Error(errorMessage);
            }
        } catch (error) {
            console.error(`Error interacting with Attendance Detail element: ${error}`);
            throw error; // Re-throw the error to fail the test if necessary
        }
    }

    /**
     * Applies date filters to the attendance detail view
     * @param fromDate - Optional start date (defaults to 30 days ago if not provided)
     * @param toDate - Optional end date (defaults to current date if not provided)
     */
    async applyAttendanceDetailFilters(fromDate?: Date | string, toDate?: Date | string) {
        const dateFilter = new DateFilter(fromDate, toDate);

        await this.page.waitForTimeout(2000);
        await this.page.fill('#c29', dateFilter.getFromDateString());
        await this.page.waitForTimeout(2000);
        await this.page.fill('#c30', dateFilter.getToDateString());
    }

    async selectRandomCampusForAttendanceDetail() {
        await this.attendanceOverviewObjects.attendanceDetailCampus();
        await this.page.click('#c34');
    }

    async isAttendanceDetailDataGridDisplayed() {
        await this.page.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight);
        });
        const dataGridDetail = this.page.locator('#c41_dataGrid table tbody');
        // Wait for the grid to be available and try to get its visibility state
        try {
            await this.page.waitForSelector('#c41_dataGrid table tbody');
            return await dataGridDetail.isVisible();
        } catch (error) {
            console.log('Waiting for data grid');
            return false;
        }
    }

    async isBriefsDataGridDisplayed() {
        await this.page.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight);
        });
        const dataGridDetail = this.page.locator('#Briefs table tbody');
        // Wait for the grid to be available and try to get its visibility state
        try {
            await this.page.waitForSelector('#Briefs table tbody');
            return await dataGridDetail.isVisible();
        } catch (error) {
            console.log('Waiting for data grid');
            return false;
        }
    }

    async navigateToStudentSuccessOverview() {
        await this.classroomManagementObjects.studentSuccessLocator();
    }

    async navigateToStudentDetailOverview() {
        await this.classroomManagementObjects.studentSuccessDetailLocator();
    }

    async applyStudentSuccessOverviewFilters() {
        // Load the CSV data first
        await this.loadStudentSuccessDetailData();

        // Select a random qualification
        await this.studentSuccessOverviewObjects.randomQualification();
        await this.page.waitForTimeout(3000);

        // Get the selected qualification text and value
        const qualificationDropdown = this.page.locator('#c5');
        const [selectedQualificationOption, selectedQualificationValue] = await qualificationDropdown.evaluate((select: HTMLSelectElement) => {
            const options = Array.from(select.options);
            const selectedOption = options.find(option => option.selected);
            return selectedOption ? [selectedOption.text, selectedOption.value] : [null, null];
        });

        if (!selectedQualificationOption) {
            throw new Error('No qualification selected');
        }

        console.log(`Selected qualification: ${selectedQualificationOption} (value: ${selectedQualificationValue})`);

        // List of qualification values that should use default year selection
        // These are qualifications that we know aren't in the CSV or won't work with the CSV approach
        const skipCsvForQualifications = ['25', '26', '27']; // Add more qualification values as needed

        if (skipCsvForQualifications.includes(selectedQualificationValue)) {
            console.log(`Qualification "${selectedQualificationOption}" (value: ${selectedQualificationValue}) is in the skip list. Using default year selection.`);

            // Default year options map for fallback
            const defaultYearOptionsMap: Record<string, string> = {
                '2023': '1',
                '2024': '2',
                '2025': '3',
                '2026': '4'
            };

            // Get the current year
            const currentYear = new Date().getFullYear().toString();
            const selectYear = this.page.locator('#c6');

            // Use the default map to get the correct option value for the current year
            const yearOptionValue = defaultYearOptionsMap[currentYear as keyof typeof defaultYearOptionsMap];
            if (yearOptionValue) {
                await selectYear.selectOption({value: yearOptionValue});
                console.log(`Selected default year option: ${yearOptionValue} for year ${currentYear}`);
            } else {
                // Fallback to selecting by label if the current year is not in the map
                await selectYear.selectOption({label: currentYear});
                console.log(`Selected year by label: ${currentYear}`);
            }
        } else {
            // Get the current year
            const currentYear = new Date().getFullYear().toString();

            // Log available qualifications in the CSV for debugging
            console.log(`Available qualifications in CSV: ${this.studentSuccessDetailData?.map(entry => entry.Qualification).join(', ')}`);

            // Find the matching entry in the CSV data
            const matchingEntries = this.studentSuccessDetailData?.filter(entry => {
                const csvQual = entry.Qualification.trim();
                const selectedQual = selectedQualificationOption.trim();
                const yearMatch = entry['Calendar Year'] === currentYear;

                // Log detailed comparison for debugging
                if (csvQual.includes(selectedQual) || selectedQual.includes(csvQual)) {
                    console.log(`Partial match: CSV="${csvQual}", Selected="${selectedQual}", Year match=${yearMatch}`);
                }

                return csvQual === selectedQual && yearMatch;
            });

            if (!matchingEntries || matchingEntries.length === 0) {
                console.log(`No exact matching entry found for qualification "${selectedQualificationOption}" and year "${currentYear}"`);

                // Try a more flexible match (contains instead of exact match)
                const flexibleMatches = this.studentSuccessDetailData?.filter(entry =>
                    (entry.Qualification.trim().includes(selectedQualificationOption.trim()) ||
                     selectedQualificationOption.trim().includes(entry.Qualification.trim())) &&
                    entry['Calendar Year'] === currentYear
                );

                if (flexibleMatches && flexibleMatches.length > 0) {
                    // Use the first flexible match
                    const flexMatch = flexibleMatches[0];
                    console.log(`Found flexible match: ${JSON.stringify(flexMatch)}`);

                    const selectYear = this.page.locator('#c6');

                    // Log available year options for debugging
                    const yearOptions = await selectYear.evaluate((select: HTMLSelectElement) => {
                        return Array.from(select.options).map(option => ({
                            text: option.text,
                            value: option.value
                        }));
                    });
                    console.log(`Available year options: ${JSON.stringify(yearOptions)}`);
                    console.log(`Attempting to select year option with value: "${flexMatch['Year Option']}"`);

                    try {
                        await selectYear.selectOption({value: flexMatch['Year Option']});
                        console.log(`Successfully selected year option from flexible match: ${flexMatch['Year Option']}`);
                    } catch (error) {
                        console.error(`Error selecting year option: ${error}`);
                        console.log('Falling back to selecting by index...');

                        // Find the index of the option with a similar value
                        const optionIndex = yearOptions.findIndex(opt =>
                            opt.value === flexMatch['Year Option'] ||
                            opt.value.includes(flexMatch['Year Option']) ||
                            flexMatch['Year Option'].includes(opt.value)
                        );

                        if (optionIndex > 0) {
                            await selectYear.selectOption({index: optionIndex});
                            console.log(`Selected year option by index: ${optionIndex}`);
                        } else {
                            // Last resort: select by label using the calendar year
                            await selectYear.selectOption({label: flexMatch['Calendar Year']});
                            console.log(`Selected year option by label: ${flexMatch['Calendar Year']}`);
                        }
                    }
                } else {
                    console.log('No flexible match found. Falling back to default year option selection');

                    // Default year options map for fallback
                    const defaultYearOptionsMap: Record<string, string> = {
                        '2023': '1',
                        '2024': '2',
                        '2025': '3',
                        '2026': '4'
                    };

                    const selectYear = this.page.locator('#c6');

                    // Use the default map to get the correct option value for the current year
                    const yearOptionValue = defaultYearOptionsMap[currentYear as keyof typeof defaultYearOptionsMap];
                    if (yearOptionValue) {
                        await selectYear.selectOption({value: yearOptionValue});
                        console.log(`Selected default year option: ${yearOptionValue} for year ${currentYear}`);
                    } else {
                        // Fallback to selecting by label if the current year is not in the map
                        await selectYear.selectOption({label: currentYear});
                        console.log(`Selected year by label: ${currentYear}`);
                    }
                }
            } else {
                // Use the year option from the CSV data
                const matchingEntry = matchingEntries[0];
                console.log(`Found exact matching entry: ${JSON.stringify(matchingEntry)}`);

                const selectYear = this.page.locator('#c6');

                // Log available year options for debugging
                const yearOptions = await selectYear.evaluate((select: HTMLSelectElement) => {
                    return Array.from(select.options).map(option => ({
                        text: option.text,
                        value: option.value
                    }));
                });
                console.log(`Available year options: ${JSON.stringify(yearOptions)}`);
                console.log(`Attempting to select year option with value: "${matchingEntry['Year Option']}"`);

                try {
                    await selectYear.selectOption({value: matchingEntry['Year Option']});
                    console.log(`Successfully selected year option: ${matchingEntry['Year Option']}`);
                } catch (error) {
                    console.error(`Error selecting year option: ${error}`);
                    console.log('Falling back to selecting by index...');

                    // Find the index of the option with a similar value
                    const optionIndex = yearOptions.findIndex(opt =>
                        opt.value === matchingEntry['Year Option'] ||
                        opt.value.includes(matchingEntry['Year Option']) ||
                        matchingEntry['Year Option'].includes(opt.value)
                    );

                    if (optionIndex > 0) {
                        await selectYear.selectOption({index: optionIndex});
                        console.log(`Selected year option by index: ${optionIndex}`);
                    } else {
                        // Last resort: select by label using the calendar year
                        await selectYear.selectOption({label: matchingEntry['Calendar Year']});
                        console.log(`Selected year option by label: ${matchingEntry['Calendar Year']}`);
                    }
                }
            }
        }

        await this.page.waitForTimeout(3000);

        const dropdownSelector = await this.page.$('select#c7');
        if (!dropdownSelector) {
            throw new Error('Dropdown selector not found');
        }
        const dropdownOptions = await dropdownSelector.$$eval('option', options => options.map(option => option.value));
        dropdownOptions.shift();
        const randomOptionIndex = dropdownOptions[Math.floor(Math.random() * dropdownOptions.length)];
        await dropdownSelector.selectOption({value: randomOptionIndex});
        await this.page.waitForTimeout(3000);
    }

    async applyStudentSuccessDetailFilters() {
        // Load the CSV data first
        await this.loadStudentSuccessDetailData();

        // Select a random qualification
        await this.studentSuccessOverviewObjects.randomQualificationStudentSuccessDetail();
        await this.page.waitForTimeout(3000);

        // Get the selected qualification text and value
        const qualificationDropdown = this.page.locator('#c1');
        const [selectedQualificationOption, selectedQualificationValue] = await qualificationDropdown.evaluate((select: HTMLSelectElement) => {
            const options = Array.from(select.options);
            const selectedOption = options.find(option => option.selected);
            return selectedOption ? [selectedOption.text, selectedOption.value] : [null, null];
        });

        if (!selectedQualificationOption) {
            throw new Error('No qualification selected');
        }

        console.log(`Selected qualification: ${selectedQualificationOption} (value: ${selectedQualificationValue})`);

        // List of qualification values that should use default year selection
        // These are qualifications that we know aren't in the CSV or won't work with the CSV approach
        const skipCsvForQualifications = ['25', '26', '27']; // Add more qualification values as needed

        if (skipCsvForQualifications.includes(selectedQualificationValue)) {
            console.log(`Qualification "${selectedQualificationOption}" (value: ${selectedQualificationValue}) is in the skip list. Using default year selection.`);

            // Default year options map for fallback
            const defaultYearOptionsMap: Record<string, string> = {
                '2023': '1',
                '2024': '2',
                '2025': '3',
                '2026': '4'
            };

            // Get the current year
            const currentYear = new Date().getFullYear().toString();
            const selectYear = this.page.locator('#c2');

            // Use the default map to get the correct option value for the current year
            const yearOptionValue = defaultYearOptionsMap[currentYear as keyof typeof defaultYearOptionsMap];
            if (yearOptionValue) {
                await selectYear.selectOption({value: yearOptionValue});
                console.log(`Selected default year option: ${yearOptionValue} for year ${currentYear}`);
            } else {
                // Fallback to selecting by label if the current year is not in the map
                await selectYear.selectOption({label: currentYear});
                console.log(`Selected year by label: ${currentYear}`);
            }
        } else {
            // Get the current year
            const currentYear = new Date().getFullYear().toString();

            // Log available qualifications in the CSV for debugging
            console.log(`Available qualifications in CSV: ${this.studentSuccessDetailData?.map(entry => entry.Qualification).join(', ')}`);

            // Find the matching entry in the CSV data
            const matchingEntries = this.studentSuccessDetailData?.filter(entry => {
                const csvQual = entry.Qualification.trim();
                const selectedQual = selectedQualificationOption.trim();
                const yearMatch = entry['Calendar Year'] === currentYear;

                // Log detailed comparison for debugging
                if (csvQual.includes(selectedQual) || selectedQual.includes(csvQual)) {
                    console.log(`Partial match: CSV="${csvQual}", Selected="${selectedQual}", Year match=${yearMatch}`);
                }

                return csvQual === selectedQual && yearMatch;
            });

            if (!matchingEntries || matchingEntries.length === 0) {
                console.log(`No exact matching entry found for qualification "${selectedQualificationOption}" and year "${currentYear}"`);

                // Try a more flexible match (contains instead of exact match)
                const flexibleMatches = this.studentSuccessDetailData?.filter(entry =>
                    (entry.Qualification.trim().includes(selectedQualificationOption.trim()) ||
                     selectedQualificationOption.trim().includes(entry.Qualification.trim())) &&
                    entry['Calendar Year'] === currentYear
                );

                if (flexibleMatches && flexibleMatches.length > 0) {
                    // Use the first flexible match
                    const flexMatch = flexibleMatches[0];
                    console.log(`Found flexible match: ${JSON.stringify(flexMatch)}`);

                    const selectYear = this.page.locator('#c2');

                    // Log available year options for debugging
                    const yearOptions = await selectYear.evaluate((select: HTMLSelectElement) => {
                        return Array.from(select.options).map(option => ({
                            text: option.text,
                            value: option.value
                        }));
                    });
                    console.log(`Available year options: ${JSON.stringify(yearOptions)}`);
                    console.log(`Attempting to select year option with value: "${flexMatch['Year Option']}"`);

                    try {
                        await selectYear.selectOption({value: flexMatch['Year Option']});
                        console.log(`Successfully selected year option from flexible match: ${flexMatch['Year Option']}`);
                    } catch (error) {
                        console.error(`Error selecting year option: ${error}`);
                        console.log('Falling back to selecting by index...');

                        // Find the index of the option with a similar value
                        const optionIndex = yearOptions.findIndex(opt =>
                            opt.value === flexMatch['Year Option'] ||
                            opt.value.includes(flexMatch['Year Option']) ||
                            flexMatch['Year Option'].includes(opt.value)
                        );

                        if (optionIndex > 0) {
                            await selectYear.selectOption({index: optionIndex});
                            console.log(`Selected year option by index: ${optionIndex}`);
                        } else {
                            // Last resort: select by label using the calendar year
                            await selectYear.selectOption({label: flexMatch['Calendar Year']});
                            console.log(`Selected year option by label: ${flexMatch['Calendar Year']}`);
                        }
                    }
                } else {
                    console.log('No flexible match found. Falling back to default year option selection');

                    // Default year options map for fallback
                    const defaultYearOptionsMap: Record<string, string> = {
                        '2023': '1',
                        '2024': '2',
                        '2025': '3',
                        '2026': '4'
                    };

                    const selectYear = this.page.locator('#c2');

                    // Use the default map to get the correct option value for the current year
                    const yearOptionValue = defaultYearOptionsMap[currentYear as keyof typeof defaultYearOptionsMap];
                    if (yearOptionValue) {
                        await selectYear.selectOption({value: yearOptionValue});
                        console.log(`Selected default year option: ${yearOptionValue} for year ${currentYear}`);
                    } else {
                        // Fallback to selecting by label if the current year is not in the map
                        await selectYear.selectOption({label: currentYear});
                        console.log(`Selected year by label: ${currentYear}`);
                    }
                }
            } else {
                // Use the year option from the CSV data
                const matchingEntry = matchingEntries[0];
                console.log(`Found exact matching entry: ${JSON.stringify(matchingEntry)}`);

                const selectYear = this.page.locator('#c2');

                // Log available year options for debugging
                const yearOptions = await selectYear.evaluate((select: HTMLSelectElement) => {
                    return Array.from(select.options).map(option => ({
                        text: option.text,
                        value: option.value
                    }));
                });
                console.log(`Available year options: ${JSON.stringify(yearOptions)}`);
                console.log(`Attempting to select year option with value: "${matchingEntry['Year Option']}"`);

                try {
                    await selectYear.selectOption({value: matchingEntry['Year Option']});
                    console.log(`Successfully selected year option: ${matchingEntry['Year Option']}`);
                } catch (error) {
                    console.error(`Error selecting year option: ${error}`);
                    console.log('Falling back to selecting by index...');

                    // Find the index of the option with a similar value
                    const optionIndex = yearOptions.findIndex(opt =>
                        opt.value === matchingEntry['Year Option'] ||
                        opt.value.includes(matchingEntry['Year Option']) ||
                        matchingEntry['Year Option'].includes(opt.value)
                    );

                    if (optionIndex > 0) {
                        await selectYear.selectOption({index: optionIndex});
                        console.log(`Selected year option by index: ${optionIndex}`);
                    } else {
                        // Last resort: select by label using the calendar year
                        await selectYear.selectOption({label: matchingEntry['Calendar Year']});
                        console.log(`Selected year option by label: ${matchingEntry['Calendar Year']}`);
                    }
                }
            }
        }

        await this.page.waitForTimeout(3000);

        const dropdownSelector = await this.page.$('select#c3');
        if (!dropdownSelector) {
            throw new Error('Dropdown selector not found');
        }
        const dropdownOptions = await dropdownSelector.$$eval('option', options => options.map(option => option.value));
        dropdownOptions.shift();
        const randomOptionIndex = dropdownOptions[Math.floor(Math.random() * dropdownOptions.length)];
        await dropdownSelector.selectOption({value: randomOptionIndex});
        await this.page.waitForTimeout(3000);

        const dropdownCourseSelector = await this.page.$('select#c4');
        if (!dropdownCourseSelector) {
            throw new Error('Dropdown course selector not found');
        }
        const dropdownCourseOptions = await dropdownCourseSelector.$$eval('option', options => options.map(option => option.value));
        dropdownCourseOptions.shift();
        const randomCourseOptionIndex = dropdownCourseOptions[Math.floor(Math.random() * dropdownCourseOptions.length)];
        await dropdownCourseSelector.selectOption({value: randomCourseOptionIndex});
        await this.page.waitForTimeout(3000);

        const applyFiltersButton = await this.page.$('#c7');
        await applyFiltersButton?.click();
        await this.page.waitForTimeout(5000);
    }

    async applyStudentSuccessOverviewFiltersAndCheckDataGrid() {
        const applyFiltersButton = await this.page.$('#c12');
        await applyFiltersButton?.click();
        await this.page.waitForTimeout(5000);

        const dataGridSuccess = this.page.locator('#c3 table tbody');
        return dataGridSuccess.isVisible();
    }

    // Add more methods for other tests...

    async navigateToTimetables() {
        await this.classroomManagementObjects.timetablesLocator();
        await this.page.waitForLoadState('networkidle');
    }

    async selectRandomCampusForTimetable() {
        await this.timetableObjects.randomCampus();
        await this.page.waitForTimeout(6000);
    }

    async selectRandomSession() {
        await this.timetableObjects.findSession();
        await this.page.waitForTimeout(3000);
        return this.page.getByText('Session Details').isVisible();
    }

    async closeSessionDetails() {
        await this.page.getByLabel('Session Details').getByText('Cancel').click();
        await this.page.waitForTimeout(3000);
    }

    async createBlankSession() {
        await this.timetableObjects.blankSession();
        await this.page.waitForTimeout(3000);
        return this.page.getByText('Session Details').isVisible();
    }

    async navigateToLateResubmissionReport() {
        await this.classroomManagementObjects.lateResubmissionsLocator();
        await this.page.waitForLoadState('networkidle');
    }

    async dateFilter(fromDate?: Date | string, toDate?: Date | string): Promise<void> {
        await this.lateResubObjects.dateFilter(fromDate, toDate);
    }

    async randomLateQualification(): Promise<void> {
        await this.lateResubObjects.randomLateQualification();
    }

    async randomAcademicYear(): Promise<void> {
        await this.lateResubObjects.randomAcademicYear();
    }

    async selectCalendarYear(): Promise<void> {
        await this.lateResubObjects.selectCalendarYear();
    }

    async getLateResubmissionData() {
        const row = 'table tbody tr';
        const totalStudentsPassed = await this.page.textContent(`${row} td:nth-child(6)`);
        const totalStudentsPassedBeforeLate = await this.page.textContent(`${row} td:nth-child(4)`);
        const averageCourseMarkAfterLate = await this.page.textContent(`${row} td:nth-child(7)`);
        const averageCourseMarkBeforeLate = await this.page.textContent(`${row} td:nth-child(5)`);
        return { totalStudentsPassed, totalStudentsPassedBeforeLate, averageCourseMarkAfterLate, averageCourseMarkBeforeLate };
    }
}
