# Check Service Principal Permissions
# This script helps verify your Service Principal has the right permissions

Write-Host "=== Service Principal Permissions Checker ===" -ForegroundColor Green

$STORAGE_ACCOUNT = "inconnecttestreports"
$APP_ID = "823dc0c0-a40b-4e6c-85c0-2848c21ce7c8"

Write-Host "Storage Account: $STORAGE_ACCOUNT" -ForegroundColor Yellow
Write-Host "Service Principal App ID: $APP_ID" -ForegroundColor Yellow

Write-Host "`n=== Manual Checks Required ===" -ForegroundColor Cyan
Write-Host "Please verify the following in Azure Portal:" -ForegroundColor White

Write-Host "`n1. Service Principal Role Assignment:" -ForegroundColor Yellow
Write-Host "   - Go to Storage Account > Access Control (IAM)" -ForegroundColor White
Write-Host "   - Check if 'playwrighttest' ($APP_ID) has one of these roles:" -ForegroundColor White
Write-Host "     • Storage Blob Data Contributor" -ForegroundColor Green
Write-Host "     • Storage Blob Data Owner" -ForegroundColor Green
Write-Host "     • Contributor" -ForegroundColor Green

Write-Host "`n2. Storage Account Network Access:" -ForegroundColor Yellow
Write-Host "   - Go to Storage Account > Networking" -ForegroundColor White
Write-Host "   - Check 'Public network access':" -ForegroundColor White
Write-Host "     • Should be 'Enabled from all networks' OR" -ForegroundColor Green
Write-Host "     • 'Enabled from selected virtual networks and IP addresses' with GitHub Actions IPs allowed" -ForegroundColor Green

Write-Host "`n3. Storage Account Configuration:" -ForegroundColor Yellow
Write-Host "   - Go to Storage Account > Static website" -ForegroundColor White
Write-Host "   - Verify 'Static website' is 'Enabled'" -ForegroundColor Green
Write-Host "   - Note the 'Primary endpoint' URL" -ForegroundColor Green

Write-Host "`n4. Container Access Level:" -ForegroundColor Yellow
Write-Host "   - Go to Storage Account > Containers > `$web" -ForegroundColor White
Write-Host "   - Check 'Public access level':" -ForegroundColor White
Write-Host "     • Can be 'Private' (Service Principal will handle auth)" -ForegroundColor Green
Write-Host "     • Or 'Blob' for public read access" -ForegroundColor Green

Write-Host "`n=== Common Issues ===" -ForegroundColor Red
Write-Host "❌ Service Principal not assigned any role on storage account" -ForegroundColor White
Write-Host "❌ Storage account has 'Disabled' public network access" -ForegroundColor White
Write-Host "❌ Firewall rules blocking GitHub Actions IP ranges" -ForegroundColor White
Write-Host "❌ Service Principal created in wrong tenant" -ForegroundColor White

Write-Host "`n=== How to Fix Role Assignment ===" -ForegroundColor Cyan
Write-Host "1. Go to Azure Portal > Storage Accounts > $STORAGE_ACCOUNT" -ForegroundColor White
Write-Host "2. Click 'Access Control (IAM)' in left menu" -ForegroundColor White
Write-Host "3. Click '+ Add' > 'Add role assignment'" -ForegroundColor White
Write-Host "4. Select role: 'Storage Blob Data Contributor'" -ForegroundColor White
Write-Host "5. Assign access to: 'User, group, or service principal'" -ForegroundColor White
Write-Host "6. Search for: 'playwrighttest' or '$APP_ID'" -ForegroundColor White
Write-Host "7. Select it and click 'Save'" -ForegroundColor White

Write-Host "`n=== Next Steps ===" -ForegroundColor Green
Write-Host "1. Verify the above settings in Azure Portal" -ForegroundColor White
Write-Host "2. Run test-url-formats.ps1 to find the working URL format" -ForegroundColor White
Write-Host "3. Update GitHub Actions workflow with working format" -ForegroundColor White
