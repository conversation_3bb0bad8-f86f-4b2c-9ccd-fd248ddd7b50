import { Page } from '@playwright/test';
import { Analytics_objects } from "../test-objects/analytics_objects";

export class AnalyticsPage {
    private page: Page;
    private analyticsObjects: Analytics_objects;

    constructor(page: Page) {
        this.page = page;
        this.analyticsObjects = new Analytics_objects(page);
    }

    async navigateToReportManager() {
        await this.analyticsObjects.reportManagerLocator();
    }

    async navigateToDataAnalytics() {
        await this.analyticsObjects.dataAnalyticsLocator();
    }

    async clickNewReportCategory() {
        // await this.page.click('button#new-category');
        await this.page.getByRole('button', { name: 'New Category' });
        await this.page.waitForTimeout(5000);
    }

    async clickNewReport() {
        // const newReportButton = this.page.locator('button#new-report');
        const newReportButton = this.page.getByRole('button', { name: 'New Report', exact: true });
        await newReportButton.click();
    }

    async isReportCategoryInputVisible() {
        // const reportCategoryInput = this.page.locator('//*[@id="c1"]');
        const reportCategoryInput = this.page.getByLabel('Report Category Name');
        return reportCategoryInput.isVisible();
    }

    async isReportNameInputVisible() {
        await this.page.waitForTimeout(5000);
        // const reportNameInput = this.page.locator('//*[@id="c1"]');
        const reportNameInput = this.page.getByLabel('Report Name');
        return reportNameInput.isVisible();
    }

    async clickRandomReport() {
        // const dataGrid = await this.page.$$('div#ReportsDataGridObj-grid table tbody tr');
        const dataGrid = await this.page.$$('tr.ReportsDataGridObj-row:not(.hidden)');
        const randomReport = Math.floor(Math.random() * dataGrid.length);
        console.log(`Random report: ${randomReport}`);
        await dataGrid[randomReport].click();
        await this.page.waitForTimeout(3000);
    }

    async isReportDataGridVisible() {
        const reportDataGrid = this.page.locator('//tbody');
        return reportDataGrid.isVisible();
    }

    async searchRandomReport() {
        const randomSearch = await this.analyticsObjects.randomReportSelection();
        const applyFilter = this.page.locator('//*[@id="ReportsDataGridObj-jdgrid-apply-filter"]');
        await applyFilter.click();
        await this.page.waitForTimeout(1000);
        return randomSearch;
    }

    async findRowWithRandomSearch(randomSearch: string) {
        await this.analyticsObjects.findRowWithRandomSearch(randomSearch);
    }

    async isReportDataGridRowVisible() {
        const reportDataGrid = await this.page.$('//div[@id="ReportsDataGridObj-grid"]//table/tbody/tr');
        return reportDataGrid?.isVisible();
    }
}
