#!/bin/bash
# Test Azure Storage Upload Script
# This script tests the same upload process locally

echo "=== Azure Storage Upload Test ==="

# Set environment variables (you'll need to set these)
export AZCOPY_AUTO_LOGIN_TYPE="SPN"
export AZCOPY_SPA_APPLICATION_ID="YOUR_APP_ID_HERE"
export AZCOPY_SPA_CLIENT_SECRET="YOUR_CLIENT_SECRET_HERE"
export AZCOPY_TENANT_ID="YOUR_TENANT_ID_HERE"

# Test variables
REPORT_DIR="test-run-$(date +%Y%m%d%H%M%S)"
STORAGE_URL="https://inconnecttestreports.z13.web.core.windows.net"

echo "Report Directory: $REPORT_DIR"
echo "Storage URL: $STORAGE_URL"

# Step 1: Test connection
echo ""
echo "=== Step 1: Testing Connection ==="
echo "Testing list command..."
if azcopy list "$STORAGE_URL/\$web" --output-type=json; then
    echo "✅ Connection test passed!"
else
    echo "❌ Connection test failed!"
    exit 1
fi

# Step 2: Test simple file upload
echo ""
echo "=== Step 2: Testing Simple File Upload ==="
echo "Test file created at $(date)" > test-file.txt

echo "Uploading test file..."
if azcopy cp "./test-file.txt" "$STORAGE_URL/\$web/test-file-$(date +%Y%m%d%H%M%S).txt" --from-to=LocalBlob; then
    echo "✅ Simple file upload passed!"
else
    echo "❌ Simple file upload failed!"
fi

rm -f test-file.txt

# Step 3: Test directory upload (if you have allure-report directory)
echo ""
echo "=== Step 3: Testing Directory Upload ==="

if [ -d "allure-report" ] && [ "$(ls -A allure-report)" ]; then
    echo "Found allure-report directory. Testing upload..."
    echo "Files to upload:"
    ls -la allure-report/
    
    # Method 1: Upload entire directory
    echo "Method 1: Upload entire directory"
    if azcopy cp "./allure-report" "$STORAGE_URL/\$web/allure/$REPORT_DIR" --recursive --from-to=LocalBlob; then
        echo "✅ Method 1 succeeded!"
    else
        echo "❌ Method 1 failed!"
        
        # Method 2: Upload contents
        echo "Method 2: Upload directory contents"
        if azcopy cp "./allure-report/*" "$STORAGE_URL/\$web/allure/$REPORT_DIR/" --recursive --from-to=LocalBlob; then
            echo "✅ Method 2 succeeded!"
        else
            echo "❌ Method 2 also failed!"
        fi
    fi
    
    echo ""
    echo "Test report should be available at:"
    echo "$STORAGE_URL/allure/$REPORT_DIR/index.html"
else
    echo "No allure-report directory found. Skipping directory upload test."
    echo "To test with actual reports, run 'npm test' first to generate reports."
fi

echo ""
echo "=== Test Complete ==="
