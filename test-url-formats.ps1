# Test Different URL Formats for Azure Storage
# This script tests various URL formats to find the one that works

Write-Host "=== Testing Different URL Formats ===" -ForegroundColor Green

# Set your actual credentials
$env:AZCOPY_AUTO_LOGIN_TYPE = "SPN"
$env:AZCOPY_SPA_APPLICATION_ID = "823dc0c0-a40b-4e6c-85c0-2848c21ce7c8"
$env:AZCOPY_SPA_CLIENT_SECRET = "****************************************"
$env:AZCOPY_TENANT_ID = "de9bcee7-1e9c-4878-9fb7-39b9116e9964"

$STORAGE_ACCOUNT = "inconnecttestreports"

# Create a test file
$testContent = "Test file created at $(Get-Date)"
$testContent | Out-File -FilePath "url-test.txt" -Encoding UTF8

# Different URL formats to try
$urlFormats = @(
    # Format 1: Blob service endpoint with $web container
    "https://$STORAGE_ACCOUNT.blob.core.windows.net/`$web/url-test-1.txt",
    
    # Format 2: Static website endpoint (what we've been using)
    "https://$STORAGE_ACCOUNT.z13.web.core.windows.net/`$web/url-test-2.txt",
    
    # Format 3: Blob service with escaped dollar sign
    "https://$STORAGE_ACCOUNT.blob.core.windows.net/\`$web/url-test-3.txt",
    
    # Format 4: Static website without $web (direct to root)
    "https://$STORAGE_ACCOUNT.z13.web.core.windows.net/url-test-4.txt",
    
    # Format 5: Blob service with double quotes around container
    "https://$STORAGE_ACCOUNT.blob.core.windows.net/`"`$web`"/url-test-5.txt"
)

Write-Host "Testing $($urlFormats.Count) different URL formats..." -ForegroundColor Yellow
Write-Host ""

$successfulFormat = $null

for ($i = 0; $i -lt $urlFormats.Count; $i++) {
    $url = $urlFormats[$i]
    Write-Host "=== Test $($i + 1): ===" -ForegroundColor Cyan
    Write-Host "URL: $url" -ForegroundColor Gray
    
    $result = & azcopy cp "./url-test.txt" "$url" --from-to=LocalBlob 2>&1
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -eq 0) {
        Write-Host "✅ SUCCESS! This format works!" -ForegroundColor Green
        $successfulFormat = $url
        Write-Host "File should be available at: $($url -replace '/url-test-\d+\.txt', '')" -ForegroundColor Yellow
        break
    } else {
        Write-Host "❌ Failed" -ForegroundColor Red
        Write-Host "Error: $result" -ForegroundColor Gray
    }
    Write-Host ""
}

# Clean up
Remove-Item "url-test.txt" -ErrorAction SilentlyContinue

if ($successfulFormat) {
    Write-Host "=== SOLUTION FOUND ===" -ForegroundColor Green
    Write-Host "Working URL format: $successfulFormat" -ForegroundColor Yellow
    
    # Extract the base pattern
    $baseUrl = $successfulFormat -replace '/url-test-\d+\.txt', ''
    Write-Host "Base URL pattern: $baseUrl" -ForegroundColor Yellow
    
    Write-Host "`nFor your GitHub Actions workflow, use:" -ForegroundColor Cyan
    Write-Host "`$STORAGE_URL = `"$baseUrl`"" -ForegroundColor White
    
} else {
    Write-Host "=== NO WORKING FORMAT FOUND ===" -ForegroundColor Red
    Write-Host "This might indicate:" -ForegroundColor Yellow
    Write-Host "1. Permission issues with your Service Principal" -ForegroundColor Yellow
    Write-Host "2. Storage account firewall blocking access" -ForegroundColor Yellow
    Write-Host "3. Service Principal not assigned to storage account" -ForegroundColor Yellow
    
    Write-Host "`nNext steps:" -ForegroundColor Cyan
    Write-Host "1. Check Service Principal permissions on storage account" -ForegroundColor White
    Write-Host "2. Verify storage account allows public access" -ForegroundColor White
    Write-Host "3. Check if there are firewall rules blocking access" -ForegroundColor White
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
