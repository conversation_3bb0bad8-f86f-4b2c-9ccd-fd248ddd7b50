import { Page } from "playwright";
import { Individual_objects } from "../test-objects/individual_objects";

export class IndividualPage {
    private page: Page;
    private individualObjects: Individual_objects;

    constructor(page: Page) {
        this.page = page;
        this.individualObjects = new Individual_objects(page);
    }

    async navigateToNewIndividual() {
        await this.individualObjects.newIndividualLocator();
    }

    async isIndividualDetailPage() {
        return this.page.url().includes('/Individual_Detail');
    }

    async selectRandomStudent() {
        await this.individualObjects.clickRandomStudentInGrid();
    }

    async navigateToDocumentsTab() {
        await this.individualObjects.clickDocumentsTab();
    }

    async clickAddDocument() {
        await this.individualObjects.clickAddDocumentButton();
    }

    async isDocumentDetailsModalVisible() {
        const modal = await this.individualObjects.getDocumentDetailsModal();
        return modal.isVisible();
    }

    async openDocumentTypeDropdown() {
        await this.individualObjects.clickDocumentTypeDropdown();
    }

    async selectRandomDocumentType() {
        return await this.individualObjects.selectRandomDocumentType();
    }
}
