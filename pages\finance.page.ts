import { Page, expect } from '@playwright/test';
import {
    ageAnalysisProd,
    bankImportProd,
    debitOrdersProd,
    debtorsAccountProd,
    financialOverviewProd
} from "../test-objects/finance_module_objects";

export class FinancePage {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    async navigateToFinancialOverview() {
        await financialOverviewProd(this.page);
    }

    async navigateToDebtorsAccount() {
        await debtorsAccountProd(this.page);
    }

    async navigateToBankImport() {
        await bankImportProd(this.page);
    }

    async navigateToDebitOrders() {
        await debitOrdersProd(this.page);
    }

    async navigateToAgeAnalysis() {
        await ageAnalysisProd(this.page);
    }

    async selectDateRange(fromDate: string, toDate: string) {
        await this.page.fill('input#c51', fromDate);
        await this.page.waitForTimeout(2000);
        await this.page.fill('input#c52', toDate);
        await this.page.click('button#c57');
    }

    async isDataGridVisible() {
        const dataGrid = await this.page.waitForSelector('#c9_dataGrid');
        return dataGrid.isVisible();
    }

    async clickExportButton() {
        await this.page.click('button#c58');
    }

    async invalidExport() {
        await this.page.click('button#c58');
        const content = await this.page.locator('.swal-content p');
        await expect(content).toHaveText('Please select a date range filter.');
        if (await content.isVisible()) {
            console.log("Error message displayed for attempting to export without a date range.");
        } else {
            console.log("Error message is not visible");
        }
    }

    async confirmExport() {
        // await this.page.waitForSelector('.progressbar-text', { state: 'visible' });
        // await this.page.waitForFunction(() => {
        //     const progressElement = document.querySelector('.progressbar-text');
        //     return progressElement && progressElement.textContent === '100%';
        // }, { timeout: 0 });
        const modalTitle = await this.page.locator('text=Progress');
        await expect(modalTitle).toBeVisible();

        // await this.page.waitForSelector('button:has-text("OK")', { state: 'visible' });
        // await this.page.click('button:has-text("OK")');
    }

    async selectFutureDebtor() {
        const c27checkbox = this.page.locator('.slider').first();
        await c27checkbox.click();
        await this.page.waitForTimeout(2000);
    }

    async applyDebtorFilter() {
        await this.page.getByRole('button', { name: 'Apply filters' }).click();
    }

    async addDebtorsAccount() {
        await this.page.getByRole('button', { name: 'Add Debtors Account' }).click();
    }

    async saveDebtorsAccountModalWithoutFind() {
        await this.page.locator('button#c25').click();
        const swalMessage = await this.page.locator('.swal-content p')
        const contentMessage = await this.page.locator('.swal-content p').innerText();
        await expect(contentMessage).toBe('Please find and select an individual to link');
        // const notedFeedbackText = this.page.locator('#notedFeedbackText');
        if (await swalMessage.isVisible()) {
            console.log("Warning message is visible");
        } else {
            console.log("Warning message is not visible");
        }
    }

    async isDebtorsAccountModalVisible() {
        const modal = this.page.getByRole('heading', { name: 'Debtors Account' });
        await expect(modal).toBeVisible();
        return true;
    }

    async clickImportButton() {
        const importButton = await this.page.waitForSelector('form#BankTransaction_OverviewForm > div.row > div > div.row > div > span > button[name="c5"]');
        await importButton.click();
    }

    async confirmImport() {
        await this.page.getByRole('button', { name: 'Confirm' }).click();
    }

    async isCsvImportModalVisible() {
        const csvImportModal = await this.page.waitForSelector('h4#importModalLabel:has-text("Import Bank Transactions")', {
            state: 'visible',
            timeout: 10000
        });
        return !!csvImportModal;
    }

    async addDebitOrderSubmission() {
        await this.page.click('div#debitOrderSubmission button[name="c2"]');
    }

    async isSelectCurrencyModalVisible() {
        const pleaseSelectCurrency = await this.page.waitForSelector('div:has-text("Please Select Currency")');
        return !!pleaseSelectCurrency;
    }

    async selectRandomCampus() {
        try {
            // Wait for and click the dropdown
            const campusDropdown = this.page.getByText('--Please Select--');
            await campusDropdown.waitFor({ state: 'visible', timeout: 5000 });
            await campusDropdown.click();

            // Wait for checkboxes to be visible and get them
            const campusElements = await this.page.getByRole('checkbox');
            await campusElements.first().waitFor({ state: 'visible', timeout: 5000 });
            const allCampuses = await campusElements.all();

            if (allCampuses.length > 1) { // Skip the "All" checkbox at index 0
                const randomIndex = Math.floor(Math.random() * (allCampuses.length - 1)) + 1;
                const randomCampus = allCampuses[randomIndex];
                
                // Wait for and get the label text before clicking
                const label = randomCampus.locator('xpath=../label');
                await label.waitFor({ state: 'visible', timeout: 5000 });
                const selectedCampusText = await label.textContent() || '';
                
                await randomCampus.click();
                return selectedCampusText;
            } else {
                throw new Error("No campus options found after dropdown opened");
            }
        } catch (error: any) {
            console.error(`Failed to select campus: ${error.message}`);
            throw error; // Re-throw to make test fail explicitly
        }
    }

    private formatDateForInput(dateStr: string): string {
        // Handle date format "DD MMM YYYY"
        const months: { [key: string]: string } = {
            'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
            'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
            'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
        };

        const [day, month, year] = dateStr.split(' ');
        const monthNum = months[month];
        
        // Pad day with leading zero if needed
        const paddedDay = day.padStart(2, '0');
        
        return `${year}-${monthNum}-${paddedDay}`;
    }

    async generateAgeAnalysisReport(fromDate: string, toDate: string) {
        // Format dates to YYYY-MM-DD for date inputs
        const formattedFromDate = this.formatDateForInput(fromDate);
        const formattedToDate = this.formatDateForInput(toDate);

        // Fill the start date input
        await this.page
            .getByLabel('Start Date')
            .fill(formattedFromDate);

        // Fill the end date input
        await this.page
            .getByLabel('End Date')
            .fill(formattedToDate);

        // Click the generate report button
        await this.page.getByRole('button', { name: 'Generate Report' }).click();

        // Wait for report generation to complete
        await this.waitForReportGeneration();
    }

    async generateAgeAnalysisReportWithoutSelectingCampus() {
        // Click generate report button
        await this.page.getByRole('button', { name: 'Generate Report' }).click();

        // Wait for and verify the error modal
        const errorModal = this.page.getByRole('dialog');
        await expect(errorModal).toBeVisible();

        // Verify modal title
        const modalTitle = errorModal.getByText('Oh No!');
        await expect(modalTitle).toBeVisible();

        // Verify error message
        const errorMessage = errorModal.getByText('Please provide at least one campus to generate report.');
        await expect(errorMessage).toBeVisible();

        // Click OK button to dismiss the modal
        await errorModal.getByRole('button', { name: 'OK' }).click();

        console.log("Error modal verified successfully");
    }

    async waitForReportGeneration() {
        try {
            // Wait for progress bar to be visible and reach 100%
            const progressBar = this.page.locator('#c10 .progress-bar-success');
            
            // Then wait for aria-valuenow to be 100
            await expect(progressBar).toHaveAttribute('aria-valuenow', '0', {
                timeout: 60000
            });
            
            // Finally wait for data grid
            const dataGrid = this.page.getByRole('table').locator('tbody');
            await expect(dataGrid).toBeVisible({
                timeout: 15000
            });

            return true;
        } catch (error: any) {
            console.error('Failed to wait for report generation:', error);
            throw error; // Let the test fail with the actual error
        }
    }
}
