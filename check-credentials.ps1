# Azure Credentials Checker
# This script helps you verify your Azure credentials are correct

Write-Host "=== Azure Credentials Checker ===" -ForegroundColor Green

# REPLACE THESE WITH YOUR ACTUAL VALUES
$APP_ID = "823dc0c0-a40b-4e6c-85c0-2848c21ce7c8"
$CLIENT_SECRET = "****************************************"  
$TENANT_ID = "de9bcee7-1e9c-4878-9fb7-39b9116e9964"

Write-Host "`nCredentials to check:" -ForegroundColor Yellow
Write-Host "App ID: $APP_ID"
Write-Host "Tenant ID: $TENANT_ID"
Write-Host "Client Secret: [HIDDEN]"

# Validate format
Write-Host "`n=== Format Validation ===" -ForegroundColor Cyan

# Check App ID format (should be GUID)
if ($APP_ID -match '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$') {
    Write-Host "✅ App ID format looks correct" -ForegroundColor Green
} else {
    Write-Host "❌ App ID format looks incorrect (should be: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx)" -ForegroundColor Red
}

# Check Tenant ID format (should be GUID)
if ($TENANT_ID -match '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$') {
    Write-Host "✅ Tenant ID format looks correct" -ForegroundColor Green
} else {
    Write-Host "❌ Tenant ID format looks incorrect (should be: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx)" -ForegroundColor Red
}

# Check Client Secret (should not be empty and not placeholder)
if ($CLIENT_SECRET -ne "YOUR_CLIENT_SECRET_HERE" -and $CLIENT_SECRET.Length -gt 10) {
    Write-Host "✅ Client Secret appears to be set" -ForegroundColor Green
} else {
    Write-Host "❌ Client Secret not set or still placeholder" -ForegroundColor Red
}

Write-Host "`n=== How to Find Your Credentials ===" -ForegroundColor Yellow
Write-Host "1. Go to Azure Portal (portal.azure.com)"
Write-Host "2. Navigate to 'Azure Active Directory' > 'App registrations'"
Write-Host "3. Find your app registration"
Write-Host "4. App ID = 'Application (client) ID' on the Overview page"
Write-Host "5. Tenant ID = 'Directory (tenant) ID' on the Overview page"
Write-Host "6. Client Secret = Create one in 'Certificates & secrets' > 'Client secrets'"

Write-Host "`n=== Required Permissions ===" -ForegroundColor Yellow
Write-Host "Your Service Principal needs:"
Write-Host "1. 'Storage Blob Data Contributor' role on the storage account"
Write-Host "2. Or 'Storage Blob Data Owner' role"
Write-Host "3. Make sure static website hosting is enabled on the storage account"

Write-Host "`n=== Next Steps ===" -ForegroundColor Cyan
Write-Host "1. Update the credentials in this script with your actual values"
Write-Host "2. Run this script again to validate formats"
Write-Host "3. Then run test-azure-upload.ps1 to test the connection"
