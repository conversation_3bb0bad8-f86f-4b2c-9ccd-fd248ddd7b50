import {Page} from "@playwright/test";

export class Communication_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Selects a random campus from a dropdown menu, avoiding specific values.
     * @returns {Promise<void>}
     */
    async communicationsLocators(): Promise<void> {
        await this.page.getByText('Communication', { exact: true }).click();
        await this.page.getByRole('link', { name: 'Message Groups' }).click();
    }

    /**
     * Selects a random message group from the data grid and enters it into the search bar.
     * @returns {Promise<string>} The name of the randomly selected message group.
     * @throws {Error} If no message groups are found.
     */
    async randomMessageGroup(): Promise<string> {
        const dataGridRows = await this.page.$$('#enrol-table tbody tr');
        const numberOfRows = dataGridRows.length; // Get the number of rows
        if (numberOfRows === 0) {
            throw new Error("No message groups found");
        }

        const groups: string[] = [];

        for (let i = 1; i <= numberOfRows; i++) { // Loop through each row
            const elementXPath = `//table[@id='enrol-table']/tbody/tr[${i}]/td[1]`; // XPath for the row
            const [option] = await this.page.$$(elementXPath); // Wait for the option and get its text
            const optionText = await option.textContent();
            if (optionText) {
                groups.push(optionText.trim());
            }
        }
        if (groups.length === 0) {
            throw new Error("No valid message groups found");
        }

        const randomSearch = groups[Math.floor(Math.random() * groups.length)]; // Choose a random group from the list
        const searchBar = this.page.locator('//input[@id=\'search-enrol\']'); // Enter the group name in the search bar
        await searchBar.fill(randomSearch);
        console.log(`Random search: ${randomSearch}`);
        await this.page.waitForTimeout(5000); // Wait for 5 seconds
        return randomSearch;
    }

    /**
     * Verifies if the Message Name field is visible.
     * @returns {Promise<void>}
     */
    async messageName(): Promise<void> {
        const messageName = this.page.getByRole('cell', { name: 'Message Name', exact: true })
        if (await messageName.isVisible()) {
            console.log("Message name field is visible. Communication setup is successful")
        }
    }
}