import { Page } from "@playwright/test";

export class Individual_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    async newIndividualLocator() {
        await this.page.getByRole('button', { name: 'New Individual' }).click();
    }

    async clickRandomStudentInGrid() {
        // Wait for the page to load properly
        await this.page.waitForTimeout(4000);
        
        // Get all rows in the current visible page
        const visibleTable = this.page.locator('#c54_dataGrid table tbody tr');
        
        // Wait for at least one row to be visible
        await visibleTable.first().waitFor({ state: 'visible', timeout: 10000 });
        
        // Get count of rows on current page
        const count = await visibleTable.count();
        
        // Select a random row from the visible ones
        const randomIndex = Math.floor(Math.random() * count);
        await visibleTable.nth(randomIndex).click();
    }

    async clickDocumentsTab() {
        await this.page.waitForTimeout(3000); // Wait for page transition
        await this.page.locator('a[href="#individual_docs"]').click();
        await this.page.waitForTimeout(2000); // Wait for tab content to load
    }

    async clickAddDocumentButton() {
        await this.page.waitForTimeout(3000); // Wait for tab content to stabilize
        const addButton = this.page.getByRole('button', { name: 'Add Document' });
        await addButton.click({ timeout: 5000 });
        await this.page.waitForTimeout(2000); // Wait for modal animation
    }

    async getDocumentDetailsModal() {
        const modal = this.page.locator('.modal-header h4#PersonalDocumentModalLabel');
        return modal;
    }

    async clickDocumentTypeDropdown() {
        // Wait for the Document Type label and dropdown to be visible
        const documentTypeLabel = this.page.getByText('Document Type');
        await documentTypeLabel.waitFor({ state: 'visible', timeout: 5000 });
        
        const dropdown = this.page.getByRole('combobox').filter({ has: documentTypeLabel });
        await dropdown.waitFor({ state: 'visible', timeout: 5000 });
        await dropdown.click({ timeout: 5000 });
    }

    async selectRandomDocumentType() {
        // Wait for the select element to be available
        const select = this.page.locator('select#c62');
        
        // Get all options to find out how many there are
        const options = await select.locator('option').all();
        
        if (options.length === 0) {
            throw new Error('No document type options found in dropdown');
        }
        
        // Generate random index
        const randomIndex = Math.floor(Math.random() * options.length);
        
        // Get the text of the option we'll select (for returning)
        const selectedOption = options[randomIndex];
        const optionText = await selectedOption.textContent();
        console.log(`Selected Document Type: ${optionText}`);
        
        // Use selectOption with the value attribute
        await select.selectOption({ index: randomIndex });
        
        return optionText?.trim() ?? '';
    }
}
