import { expect, Page } from '@playwright/test';
import { Policies_procedures_objects } from "../test-objects/policies_procedures_objects";
import * as fs from 'fs/promises';
import * as path from 'path';

export class PoliciesProceduresPage {
    private page: Page;
    private policiesObjects: Policies_procedures_objects;

    constructor(page: Page) {
        this.page = page;
        this.policiesObjects = new Policies_procedures_objects(page);
    }

    /**
     * Navigates to the Policies & Procedures section
     */
    async navigateToPoliciesProcedures() {
        await this.policiesObjects.policiesLocator();
    }

    /**
     * Downloads a random policy document and verifies it
     * @param outputDir Directory to save the downloaded file
     * @returns Path to the downloaded file
     */
    async downloadRandomPolicy(outputDir: string): Promise<string> {
        // Start download process
        const downloadPromise = this.page.waitForEvent('download', { timeout: 30000 });
        
        // Get a random policy and click its download button
        const { button, name } = await this.policiesObjects.getRandomPolicyButton();
        await button.click();
        
        // Handle any confirmation dialog
        await this.policiesObjects.handleConfirmationDialog();

        // Wait for and handle the download
        const download = await downloadPromise;
        const suggestedName = download.suggestedFilename();
        const sanitizedName = name.replace(/[^a-z0-9]/gi, '_').toLowerCase();
        const filePath = path.join(outputDir, `${sanitizedName}-${suggestedName}`);
        
        await download.saveAs(filePath);
        console.log(`Downloaded policy "${name}" to: ${filePath}`);

        // Verify the download
        await this.verifyDownloadedFile(filePath);

        return filePath;
    }

    /**
     * Verifies that the downloaded file is valid
     * @param filePath Path to the downloaded file
     */
    private async verifyDownloadedFile(filePath: string): Promise<void> {
        const fileStats = await fs.stat(filePath);
        expect(fileStats.size, 'Downloaded file should not be empty').toBeGreaterThan(0);

        if (filePath.toLowerCase().endsWith('.pdf')) {
            const fileContent = await fs.readFile(filePath);
            const isPDF = fileContent.toString('ascii').startsWith('%PDF-');
            expect(isPDF, 'File should be a valid PDF').toBeTruthy();
        }
    }

    /**
     * Checks if a PDF viewer opens in a new tab
     * @returns true if PDF viewer is displayed, false otherwise
     */
    async checkPDFViewer(): Promise<boolean> {
        const newPagePromise = this.page.context().waitForEvent('page', { timeout: 5000 }).catch(() => null);
        
        try {
            const newPage = await newPagePromise;
            if (!newPage) {
                return false;
            }

            try {
                await newPage.waitForLoadState('networkidle', { timeout: 10000 });
                const fileUrl = newPage.url();
                
                const isPDF = fileUrl.includes('.pdf');
                if (isPDF) {
                    console.log('Document opened in PDF viewer');
                }
                return isPDF;
            } finally {
                if (!newPage.isClosed()) {
                    await newPage.close();
                }
            }
        } catch (error: any) {
            console.error('Error checking PDF viewer:', error);
            return false;
        }
    }
}