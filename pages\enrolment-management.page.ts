import { Page, expect } from '@playwright/test';
import { Enrolment_management_objects } from "../test-objects/enrolment_management_objects";

export class EnrolmentManagementPage {
    private page: Page;
    private enrolmentManagementObjects: Enrolment_management_objects;

    constructor(page: Page) {
        this.page = page;
        this.enrolmentManagementObjects = new Enrolment_management_objects(page);
    }

    async navigateToEnrolmentManagement() {
        await this.enrolmentManagementObjects.enrolmentLocator();
    }

    async filterActiveEnrolments() {
        await this.enrolmentManagementObjects.enrolmentFilterActive();
    }

    async filterEnrolledEnrolments() {
        await this.enrolmentManagementObjects.enrolmentFilterEnrolled();
    }

    async selectRandomEnrolment() {
        await this.enrolmentManagementObjects.selectRandomEnrolment();
    }

    async isDataGridVisible() {
        const dataGrid = this.page.locator('c8_dataGrid');
        return await dataGrid.isVisible();
    }

    async navigateToDocumentsTab() {
        await this.page.click('a[href="#documents"]');
        await this.page.waitForTimeout(3000);
    }

    async uploadDocument() {
        const uploadButtons = await this.page.$$('button.btn.fullWidth.pull-right');
        if (uploadButtons.length > 0) {
            const randomButtonIndex = Math.floor(Math.random() * uploadButtons.length);
            const randomButton = uploadButtons[randomButtonIndex];
            await randomButton.click();
        } else {
            console.error("No upload buttons found");
        }
        await this.page.waitForTimeout(4000);
    }

    async isUploadModalVisible() {
        const uploadModal = this.page.locator('h4.modal-title#SupportingDocumentsModalLabel');
        return await uploadModal.isVisible();
    }

    async getUploadModalText() {
        const uploadModal = this.page.locator('h4.modal-title#SupportingDocumentsModalLabel');
        return await uploadModal.textContent();
    }

    async navigateToNotesTab() {
        await this.page.getByRole('link', {name: 'Notes'}).click();
    }

    async navigateToChangeDetailsTab() {
        await this.page.getByRole('link', {name: 'Change Details'}).click();
    }

    async addNote(noteText: string) {
        await this.page.getByRole('button', {name: ' New Note'}).click();
        await this.page.getByLabel('Note', {exact: true}).fill(noteText);
        await this.page.getByLabel('Enrolment Note Details').getByText('Save').click();
    }

    async deleteNote(noteText: string) {
        await this.page.getByRole('cell', {name: noteText}).click();
        await this.page.getByLabel('Enrolment Note Details').getByText('Delete').click();
        await this.page.getByRole('button', {name: 'Yes'}).click();
    }

    async deleteEmptyNote(noteText: string) {
        await this.page.getByRole('cell', {name: noteText}).nth(13).click();
        await this.page.getByLabel('Enrolment Note Details').getByText('Delete').click();
        await this.page.getByRole('button', {name: 'Yes'}).click();
    }

    async isEmptyNoteVisible(noteText: string) {
        const emptyNote = await this.page.getByRole('cell', {name: noteText})
        const isEmptyNoteVisible = await emptyNote.count() > 0;
        if (isEmptyNoteVisible) {
            console.log("Empty Note is visible. An empty note can be added.")
        } else {
            console.log("Empty Note is not visible. An empty note cannot be added.")
        }
    }

    async isNoteVisible(noteText: string) {
        const noteCell = this.page.getByRole('cell', {name: noteText});
        return await noteCell.isVisible();
    }

    async generateProformaInvoice() {
        await this.page.getByRole('button', {name: 'Generate Proforma'}).click();
        await this.page.getByText('Generate Proforma Invoice').click();
        await this.page.getByLabel('Enrolment Payment Type').selectOption('1');
        await this.page.getByLabel('Enrolment Payment Method').selectOption('4');
        const downloadPromise = this.page.waitForEvent('download');
        await this.page.getByText('Download', {exact: true}).click();
        const download = await downloadPromise;
        return download.url();
    }

    async generateContractOfEnrolment() {
        const newPagePromise = this.page.context().waitForEvent('page');
        await this.page.getByRole('button', {name: 'Generate COE'}).click();
        const newPage = await newPagePromise;
        await newPage.waitForLoadState('networkidle');
        const newPageUrl = newPage.url();
        await newPage.close();
        return newPageUrl;
    }

    async generateEnrolmentLetter() {
        await this.page.getByRole('button', {name: 'Enrolment Letter'}).click();
        const downloadPromise = this.page.waitForEvent('download');
        const download = await downloadPromise;
        return download.url();
    }

    async applyFilter(eligibleYear: string) {
        // const eligibleYear = (new Date().getFullYear() + 1).toString();
        console.log("Enrolling Year: " + eligibleYear)
        const checkbox = this.page.locator(`input[type="checkbox"][id*="_opt_ctl"][name="c22"] + label:has-text("${eligibleYear}")`);
        await checkbox.click();
    } 

    async navigateWithoutLogin() {
    const enrolmentManagementUrl = process.env.ENV === 'accp' 
    ? 'https://inconnect-accp-operations.stratusolvecloud.com/App/SalesAgent/EnrolmentManagement/' 
    : 'https://operations.inscape.co.za/App/SalesAgent/EnrolmentManagement/';

    const loginUrl = process.env.ENV === 'accp' 
    ? 'https://inconnect-accp-connect.stratusolvecloud.com/UserManagement/login/' 
    : 'https://connect.inscape.co.za/UserManagement/login/';
    
    await this.page.goto(enrolmentManagementUrl);
    await this.page.waitForTimeout(1000)
    expect(this.page.url()).toContain(loginUrl);
    console.log(loginUrl)
    }

    async generateCourseFeePaymentPlan() {
        const contractTypeDropdown = await this.page.locator('#c152');
        const currentOption = await contractTypeDropdown.locator('option:checked').getAttribute('value');
        
        // If Early Bird (value="1") is selected, switch to Standard (value="3")
        // If Standard (value="3") is selected, switch to Early Bird (value="1")
        const newValue = currentOption === '1' ? '3' : '1';
        
        await contractTypeDropdown.selectOption(newValue);
        await this.page.waitForTimeout(1000); // Wait for selection to take effect
        
        const selectedOption = await contractTypeDropdown.locator('option:checked').innerText();
        console.log("Selected option: " + selectedOption);
        expect(selectedOption).not.toBe('--Please Select--');
        
        await this.page.waitForTimeout(2000);
        await this.page.getByRole('button', {name: 'Generate Payment plan & Milestones'}).first().click();

        const yesButton = this.page.locator('button#c176');
        await expect(yesButton).toBeVisible();
        await yesButton.click();
        await this.page.waitForTimeout(2000);

        const successMessage = await this.page.locator('.swal-title').textContent();
        expect(successMessage).toBe('Success!');
        const contentMessage = await this.page.locator('.swal-content p').textContent();
        expect(contentMessage).toBe('Successfully generated payment plan and associated milestones');

        const okButton = this.page.locator('.swal-button--confirm');
        await expect(okButton).toBeVisible();
        await okButton.click();
    }

    async confirmPaymentPlanGeneration(extractType: 'firstName' | 'idNumber' = 'firstName') {
        const studentNameSelector = 'div.col-md-8';
        const studentNameElement = await this.page.locator(studentNameSelector);
        await expect(studentNameElement).toBeVisible();

        const fullText = await studentNameElement.innerText();
        console.log(`Full text: ${fullText}`);

        // Extract either first name or ID number based on the parameter
        let extractedText: string;
        if (extractType === 'firstName') {
            // Format is "LastName FirstName (ID)" - get the first name
            const namePart = fullText.split('(')[0].trim();  // Get everything before the ID
            const names = namePart.split(' ');
            extractedText = names[1] || fullText; // Get the second word (first name)
        } else {
            // Extract ID number between parentheses
            const idMatch = fullText.match(/\((\d+)\)/);
            extractedText = idMatch ? idMatch[1] : fullText;
        }

        console.log(`Extracted ${extractType}: ${extractedText}`);

        // Rest of the method remains the same
        await this.page.evaluate((text) => {
            navigator.clipboard.writeText(text);
        }, extractedText);

        await this.page.click('text=Individual');
        await this.page.waitForTimeout(2000);

        const searchInput = this.page.locator('#c54_searchBox');
        await searchInput.fill(extractedText);
        await searchInput.press('Enter');

        await this.page.waitForSelector('#c54_dataGrid');
        const studentRow = await this.page.locator('tr').filter({ hasText: extractedText });
        await expect(studentRow).toBeVisible();
        await studentRow.click();
    }

    async findSpecificPaymentPlan() {
        // Click on the "Finances" tab
        await this.page.click('a.finaces-tab-link');
        await this.page.waitForTimeout(2000);
        // Wait for the relevant financial information to load
        await this.page.getByRole('tab', {name: 'Payment Plans'}).click();
        await this.page.waitForSelector('#c87_dataGrid');
        const rows = await this.page.$$('tr');
        const paymentPlanRow = await Promise.all(
            rows.map(async (row) => {
                const text = await row.textContent();
                return text && 
                       (text.includes('Early Bird') || text.includes('Standard')) && 
                       text.includes('2025') ? row : null;
            })
        );
        const filteredRow = paymentPlanRow.find(row => row !== null);
        if (filteredRow) {
            const locator = this.page.locator(await filteredRow.evaluate(el => {
                return CSS.escape(el.tagName.toLowerCase()) + 
                       Array.from(el.attributes)
                           .map(attr => `[${attr.name}="${attr.value}"]`)
                           .join('');
            }));
            await expect(locator).toBeVisible();
            console.log("Payment plan found and is visible");
        } else {
            console.log("No matching payment plan found");
        }
    }
}