import { Page } from '@playwright/test';
import { ComplianceObjects } from "../test-objects/compliance_objects";

export class CompliancePage {
    private page: Page;
    private complianceObjects: ComplianceObjects;

    constructor(page: Page) {
        this.page = page;
        this.complianceObjects = new ComplianceObjects(page);
    }

    async navigateToCompliance() {
        await this.complianceObjects.navigateToCompliance();
    }

    async selectAndDownloadNLRDDataSubmissionFiles(): Promise<Array<{ category: string, downloadPromise: Promise<any> }>> {
        const downloadPromises: Array<{ category: string, downloadPromise: Promise<any> }> = [];
        const categories = [
            'Personal Information',
            'Qualification Enrolment',
            'Staff Employment Detail',
            'Student FTE File'
        ];

        for (const category of categories) {
            await this.complianceObjects.selectCategory(category);
            await this.complianceObjects.selectCampus();
            // Start waiting for download before clicking generate
            const downloadPromise = this.page.waitForEvent('download');
            await this.complianceObjects.generateReport();
            downloadPromises.push({ category, downloadPromise });
        }

        return downloadPromises;
    }

    async selectAndDownloadDHETReportFiles(): Promise<Array<{ name: string, downloadPromise: Promise<any> }>> {
        const downloadPromises: Array<{ name: string, downloadPromise: Promise<any> }> = [];
        const reports = [
            { name: 'Age Category', option: '7' },
            { name: 'Foreign Nationals Enrolment' },
            { name: 'Number of Graduates' },
            { name: 'Number of Programmes Offered by the Institution on the HEQSF' },
            { name: 'Number of Programmes Offered on the NQF & Premises of Teaching & Learning' },
            { name: 'Number of Staff Members' },
            { name: 'Student Enrolment per Programme' },
            { name: 'Student Success Rate', option: '7' }
        ];

        for (const report of reports) {
            await this.page.getByRole('cell', { name: report.name }).click();
            if (report.option) {
                await this.page.getByRole('combobox').selectOption(report.option);
            }
            // Start waiting for download before clicking generate
            const downloadPromise = this.page.waitForEvent('download');
            await this.complianceObjects.generateReport();
            downloadPromises.push({ name: report.name, downloadPromise });
        }

        return downloadPromises;
    }
}
