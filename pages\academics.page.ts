import { Page } from '@playwright/test';
import { Academics_objects } from "../test-objects/academics_objects";

export class AcademicsPage {
    private page: Page;
    private academicsObjects: Academics_objects;

    constructor(page: Page) {
        this.page = page;
        this.academicsObjects = new Academics_objects(page);
    }

    async navigateToAcademics() {
        await this.academicsObjects.academicsLocator();
        await this.page.waitForTimeout(3000);
    }

    async createQualification() {
        await this.page.click('form#Qualification_OverviewForm ul[role="tablist"] li:nth-child(3) a[role="tab"]');
        await this.page.click('div#qualifications button[name="c1"]');
        return this.page.waitForSelector('h4#QualificationModalLabel');
    }

    async createCourse() {
        const addCourseButton = await this.page.waitForSelector('button#AddCourseBtn');
        await addCourseButton.click();
        return this.page.waitForSelector('h4#CourseModalLabel');
    }

    async createSubject() {
        await this.academicsObjects.filterCourses();
        await this.academicsObjects.randomCourseAndSubjectTab();
        const addSubjectButton = this.page.getByRole("button", {name: "Add Subject"});
        await addSubjectButton.click();
        return this.page.getByRole("heading", {name: "Course Module Details"});
    }

    async createBrief() {
        await this.academicsObjects.filterCourses();
        await this.academicsObjects.randomCourseAndSubjectTab();
        await this.academicsObjects.selectRandomSubject();
        const buttonAddDocument = this.page.getByRole("button", {name: "Add Document"});
        await buttonAddDocument.click();
        await this.page.waitForTimeout(5000);
        return this.page.getByRole("heading", {name: "Document Details"});
    }

    async accessOffsiteProduct() {
        await this.academicsObjects.filterCoursesOffsite();
        await this.academicsObjects.randomCourseAndSubjectTab();
        await this.academicsObjects.selectRandomSubject();
        const buttonDropdown = this.page.locator('#c362');
        await buttonDropdown.click();
        await this.page.locator('li:has-text("Go To")').click();
        await this.page.waitForTimeout(3000);
        await this.page.click('button[name=viewContent]');
        return this.page.waitForSelector('text=Overview');
    }

    async accessRise360Concepts() {
        await this.academicsObjects.filterCoursesRise360();
        await this.academicsObjects.randomCourseAndSubjectTab();
        await this.academicsObjects.selectRandomSubject();
        const coursewareLink = this.page.locator('a[href="#courseware"]');
        await coursewareLink.click();

        // Use locator instead of $ to avoid null checks
        const tableRow = this.page.locator('#c317_dataGrid table tbody tr').first();
        await tableRow.waitFor({ state: 'attached' });
        await tableRow.click();
        await this.page.waitForTimeout(3000);

        // Use locator for input field as well
        const inputField = this.page.locator('#concept');
        await inputField.waitFor({ state: 'attached' });
        const isDisabled = await inputField.getAttribute('disabled');
        return isDisabled !== null;
    }

    async previewRise360Course() {
        await this.page.click('button:has-text("Preview")');
        return new Promise<boolean>(resolve => {
            this.page.once('popup', async (popup) => {
                await popup.waitForLoadState('domcontentloaded');
                const startCourseButton = popup.getByRole('link', {name: 'START COURSE'});
                await startCourseButton.click();
                await popup.waitForTimeout(3000);
                const lessonTitle = popup.locator('#page-wrap').getByText('Subject Overview');
                resolve(lessonTitle !== null);
            });
        });
    }

    async randomCourse() {
        await this.academicsObjects.filterCourses();
        await this.academicsObjects.randomCourse();
        await this.page.waitForTimeout(3000);
    }

    async fillCommencementDate(date: string) {
        await this.page.fill('#c10', date);
    }

    async fillEndDate(date: string) {
        await this.page.fill('#c11', date);
    }

    async getCommencementDateValue() {
        return await this.page.inputValue('#c10');
    }

    async getEndDateValue() {
        return await this.page.inputValue('#c11');
    }

    async clickSubmitButton() {
        await this.page.click('button[name=c7]');
    }

    async getErrorMessage() {
        const errorMessage = await this.page.locator('.error-message-selector'); // Replace with actual error message selector
        return errorMessage;
    }
}
