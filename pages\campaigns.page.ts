import { Page } from '@playwright/test';
import { Campaign_objects } from "../test-objects/campaign_objects";

export class CampaignsPage {
    private page: Page;
    private campaignObjects: Campaign_objects;

    constructor(page: Page) {
        this.page = page;
        this.campaignObjects = new Campaign_objects(page);
    }

    async navigateToCampaigns() {
        await this.campaignObjects.campaignLocator();
    }

    async selectRandomCampaign() {
        await this.campaignObjects.randomCampaignSelection();
    }

    async clickNewLeadButton() {
        await this.page.click('button#c14');
        await this.page.waitForTimeout(4000);
    }

    async isNewLeadModalVisible() {
        const confirmTestNewLead = this.page.locator('#LeadModalLabel >> text=Lead Details');
        await this.page.waitForTimeout(2000);
        return confirmTestNewLead.isVisible();
    }

    async clickNewCampaignButton() {
        await this.page.click('//*[@id="c1"]');
        await this.page.waitForTimeout(2000);
    }

    async isNewCampaignModalVisible() {
        const campaignModal = await this.page.$('h4#CampaignDetailsModalLabel');
        return campaignModal ? campaignModal.isVisible() : false;
    }

    async clickCaptureSessions() {
        const linkCaptureSessions = this.page.getByRole("link", {name: "Capture Sessions"});
        await linkCaptureSessions.click();
        await this.page.waitForTimeout(1000);
    }

    async clickNewSession() {
        const buttonNewSession = this.page.getByRole("button", {name: " New Session"});
        await buttonNewSession.click();
        await this.page.waitForTimeout(1000);
    }

    async isNewSessionModalVisible() {
        const sessionModal = await this.page.$('h4#LeadCaptureSessionModalLabel');
        return sessionModal ? sessionModal.isVisible() : false;
    }

    async clickImportLeadsButton() {
        await this.page.click('//div[@id="crm_leads"]/div/div[2]/div[@class="row"]/div[2]/span/button[@name="c15"]');
        await this.page.waitForTimeout(1000);
    }

    async selectLeadGenerator() {
        await this.page.click('//form[@id="CampaignCategory_Detail"]/span[1]/div/div[@role="dialog"]//ul/li[1]');
        await this.page.waitForTimeout(2000);
    }

    async selectLeadManager() {
        await this.page.click('//form[@id="CampaignCategory_Detail"]/span[2]/div/div[@role="dialog"]//ul/li[1]');
        await this.page.waitForTimeout(2000);
    }

    async isLeadUploadModalVisible() {
        const fileUploadModal = await this.page.$('//div[@id="importModal"]/div[@class="modal-dialog modal-lg"]//div[@class="modal-header"]');
        return fileUploadModal ? fileUploadModal.isVisible() : false;
    }
}
