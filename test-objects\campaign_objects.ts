import {Page} from "@playwright/test";

export class Campaign_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Navigates to the Campaigns section in the CRM.
     * @returns {Promise<void>}
     */
    async campaignLocator(): Promise<void> {
        await this.page.click('text=CRM');
        await this.page.click('text=Campaigns');
        await this.page.waitForTimeout(5000);
    }

    /**
     * Selects a random campaign from the campaign categories and rows.
     * @returns {Promise<void>}
     */
    async randomCampaignSelection(): Promise<void> {
        const tiles = await this.page.$$('.panel.panel-default.tile.tile-hover');
        const randomTile = tiles[Math.floor(Math.random() * tiles.length)];
        const tileText = await randomTile.innerText();
        console.log(`Selected campaign category: ${tileText}`);
        await randomTile.click(); // Click on a random campaign category
        await this.page.waitForTimeout(3000);

        const rows = await this.page.$$('div#CampaignDataGrid table tbody tr');
        const randomRow = Math.floor(Math.random() * rows.length);
        const rowText = await rows[randomRow].innerText();
        console.log(`Selected campaign row: ${rowText}`);
        await rows[randomRow].click(); // Click on a random campaign
        await this.page.waitForTimeout(5000);
    }
}