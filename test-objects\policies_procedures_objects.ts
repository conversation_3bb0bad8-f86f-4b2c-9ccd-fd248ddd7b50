import { Page } from "@playwright/test";

export class Policies_procedures_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Navigates to the Policies and Procedures section and adjusts the display settings.
     * @returns {Promise<void>}
     */
    async policiesLocator(): Promise<void> {
        await this.page.getByRole('link', { name: 'Policies And Procedures' }).click();
        await this.page.waitForTimeout(3000);
        // Increase items per page for better random selection
        await this.page.getByRole('button', { name: '20' }).click();
        await this.page.getByText('50', { exact: true }).click();
    }

    /**
     * Gets a download button for a random policy.
     * @returns {Promise<string>} The name of the selected policy
     */
    async getRandomPolicyButton(): Promise<{ button: any, name: string }> {
        await this.page.waitForTimeout(3000); // Wait for list to load
        const policyRows = await this.page.$$('tr');
        
        if (policyRows.length === 0) {
            throw new Error('No policies found in the table');
        }
        
        const randomIndex = Math.floor(Math.random() * policyRows.length);
        const row = policyRows[randomIndex];
        
        // Get policy name from the row
        const nameCell = await row.$('td');
        const name = await nameCell?.textContent() || 'Unknown Policy';
        
        // Get the download button from the row
        const button = await row.$('button.btn.btn-link');
        if (!button) {
            throw new Error('No download button found in selected row');
        }
        
        return { button, name };
    }

    /**
     * Handles the download confirmation dialog if it appears.
     * @returns {Promise<void>}
     */
    async handleConfirmationDialog(): Promise<void> {
        try {
            const modal = await this.page.waitForSelector('.swal-modal', { timeout: 2000 });
            await modal.waitForSelector('.swal-title');
            await this.page.getByRole('button', { name: 'Confirm' }).click();
            await this.page.waitForTimeout(1000);
        } catch (error: any) {
            if (!(error?.name === 'TimeoutError')) {
                throw error;
            }
            // No modal appeared, which is fine
        }
    }
}