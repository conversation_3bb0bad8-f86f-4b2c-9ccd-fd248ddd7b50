import {Page} from "@playwright/test";
import * as allure from "allure-js-commons";

export class Enrolment_management_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Navigates to the Enrolment Management section.
     * @returns {Promise<void>}
     */
    async enrolmentLocator(): Promise<void> {
        await this.page.click('text=Enrolments'); // Click on enrolment navbar
        await this.page.click('a:has-text("Enrolment Management")'); // Click on Enrolment link
        await this.page.waitForLoadState('networkidle');
    }

    /**
     * Toggles the Distance and Re-enrol buttons and filters the data grid by the eligible year. Also, filters the data grid by the Active status.
     * @returns {Promise<void>}
     */
    async enrolmentFilterActive(): Promise<void> {
        await allure.step("Toggle the Distance and Re-enrol buttons", async () => {
            await this.page.waitForTimeout(2000)
            const distanceToggle = this.page.locator('//form[@id=\'EnrolmentManagement\']/div[@class=\'col-xs-12\']/div[1]/div[1]/span[2]//label/span')
            await distanceToggle.click();
            const reEnrolToggle = this.page.locator('//form[@id=\'EnrolmentManagement\']/div[@class=\'col-xs-12\']/div[1]/div[1]/span[3]//label/span')
            await reEnrolToggle.click();
            await this.page.waitForTimeout(5000)
        });

        await allure.step("Filter the data grid", async () => {
            await this.page.click('#c4')
            const eligibleYear = (new Date().getFullYear()).toString();
            console.log("Enrolling Year: " + eligibleYear)
            await this.page.click('.customselect-dropdown');
            await this.page.click('//div[@id=\'c4_DropdownMenu\']/div/div[10]/div[@class=\'form-group\']/div[@class=\'customselect-dropdown form-control\']');
            const allCheckboxSelector = '#c22_all_ctl';
            await this.page.click(allCheckboxSelector);
            const checkbox = this.page.locator(`input[type="checkbox"][id*="_opt_ctl"][name="c22"] + label:has-text("${eligibleYear}")`);
            await checkbox.click();
            await allure.attachment("EnrollingYearSelection.png", await this.page.screenshot(), {
                contentType: "image/png",
            });
            await this.page.click('#c4')
            await this.page.getByRole('button', {name: 'APPLY'}).click();
            await this.page.waitForTimeout(4000)
            await this.page.getByText('Active').click()
            await this.page.waitForTimeout(5000)
        });
    }

    /**
     * Toggles the Distance and Re-enrol buttons and filters the data grid by the eligible year. Also, filters the data grid by the Enrolled status.
     * @returns {Promise<void>}
     */
    async enrolmentFilterEnrolled(): Promise<void> {
        await allure.step("Toggle the Distance and Re-enrol buttons", async () => {
            await this.page.waitForTimeout(2000)
            const distanceToggle = this.page.locator('//form[@id=\'EnrolmentManagement\']/div[@class=\'col-xs-12\']/div[1]/div[1]/span[2]//label/span')
            await distanceToggle.click();
            const reEnrolToggle = this.page.locator('//form[@id=\'EnrolmentManagement\']/div[@class=\'col-xs-12\']/div[1]/div[1]/span[3]//label/span')
            await reEnrolToggle.click();
            await this.page.waitForTimeout(5000)
        });

        await allure.step("Filter the data grid", async () => {
            await this.page.click('#c4')
            const eligibleYear = (new Date().getFullYear() + 1).toString();
            console.log("Enrolling Year: " + eligibleYear)
            await this.page.click('.customselect-dropdown');
            await this.page.click('//div[@id=\'c4_DropdownMenu\']/div/div[10]/div[@class=\'form-group\']/div[@class=\'customselect-dropdown form-control\']');
            const allCheckboxSelector = '#c22_all_ctl';
            await this.page.click(allCheckboxSelector);
            const checkbox = this.page.locator(`input[type="checkbox"][id*="_opt_ctl"][name="c22"] + label:has-text("${eligibleYear}")`);
            await checkbox.click();
            await allure.attachment("EnrollingYearSelection.png", await this.page.screenshot(), {
                contentType: "image/png",
            });
            await this.page.click('#c4')
            await this.page.getByRole('button', {name: 'APPLY'}).click();
            await this.page.waitForTimeout(4000)
            await this.page.click("#c30")
            await this.page.waitForTimeout(5000)
        });
    }

    /**
     * Selects a random enrolment row from the data grid.
     * @returns {Promise<void>}
     */
    async selectRandomEnrolment(): Promise<void> {
        const randomRow = Math.floor(Math.random() * 20) + 1;
        const enrolmentRows = `//*[@id="c41_dataGrid"]/table/tbody/tr[${randomRow}]`;
        await this.page.waitForSelector(enrolmentRows);
        const enrolmentRow = this.page.$(enrolmentRows);
        if (enrolmentRow) {
            await (await enrolmentRow).click();
        } else {
            console.log(`Row ${randomRow} is not available in the data grid`);
        }
    }
}
